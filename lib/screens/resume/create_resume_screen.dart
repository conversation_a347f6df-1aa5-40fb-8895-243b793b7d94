import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../utils/design_tokens.dart';
import '../../models/resume_model.dart';
import '../../models/work_experience_model.dart';
import '../../models/education_model.dart';
import '../../models/skill_model.dart';
import '../../models/ai_chat_model.dart';
import '../../widgets/resume/personal_info_form.dart';
import '../../widgets/resume/work_experience_form.dart';
import '../../widgets/resume/education_form.dart';
import '../../widgets/resume/skills_form.dart';
import '../../widgets/resume/summary_form.dart';
import '../../widgets/resume/review_step.dart';
import '../../screens/ai_assistant/ai_assistant_screen.dart';
import '../../services/pdf_generation_service.dart';
import '../../services/storage_service.dart';
import '../../services/firestore_service.dart';
import '../../services/auth_service.dart';

class CreateResumeScreen extends StatefulWidget {
  const CreateResumeScreen({super.key});

  @override
  State<CreateResumeScreen> createState() => _CreateResumeScreenState();
}

class _CreateResumeScreenState extends State<CreateResumeScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 6;

  final List<String> _stepTitles = [
    'Choose Template',
    'Personal Info',
    'Summary',
    'Experience',
    'Education & Skills',
    'Review & Generate',
  ];

  // Resume data
  ResumeStyle _selectedStyle = ResumeStyle.professional;
  PersonalInfo _personalInfo = const PersonalInfo(
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
  );
  String _summary = '';
  List<WorkExperience> _workExperience = [];
  List<Education> _education = [];
  List<Skill> _skills = [];

  // Services
  final PdfGenerationService _pdfService = PdfGenerationService();
  final StorageService _storageService = StorageService();
  final FirestoreService _firestoreService = FirestoreService();
  final AuthService _authService = AuthService();

  // State
  bool _isGenerating = false;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Resume'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Modern Progress Indicator
          Container(
            padding: const EdgeInsets.all(DesignTokens.spaceLg),
            margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spaceLg),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(DesignTokens.radiusXl),
              boxShadow: DesignTokens.shadowSm,
              border: Border.all(color: DesignTokens.neutral200),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    for (int i = 0; i < _totalSteps; i++) ...[
                      Expanded(
                        child: Container(
                          height: 6,
                          decoration: BoxDecoration(
                            color: i <= _currentStep
                                ? DesignTokens.primaryBlue
                                : DesignTokens.neutral300,
                            borderRadius: BorderRadius.circular(DesignTokens.radiusXs),
                          ),
                        ),
                      ),
                      if (i < _totalSteps - 1) const SizedBox(width: DesignTokens.spaceSm),
                    ],
                  ],
                ),
                const SizedBox(height: DesignTokens.spaceLg),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignTokens.spaceMd,
                        vertical: DesignTokens.spaceXs,
                      ),
                      decoration: BoxDecoration(
                        color: DesignTokens.primaryBlue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(DesignTokens.radiusSm),
                      ),
                      child: Text(
                        'Step ${_currentStep + 1} of $_totalSteps',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: DesignTokens.primaryBlue,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    Text(
                      _stepTitles[_currentStep],
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: DesignTokens.textPrimary,
                        letterSpacing: -0.1,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Step Content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildTemplateStep(),
                _buildPersonalInfoStep(),
                _buildSummaryStep(),
                _buildExperienceStep(),
                _buildEducationAndSkillsStep(),
                _buildReviewStep(),
              ],
            ),
          ),
          
          // Navigation Buttons
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                if (_currentStep > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousStep,
                      child: const Text('Previous'),
                    ),
                  ),
                if (_currentStep > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _currentStep < _totalSteps - 1 ? _nextStep : _generateResume,
                    child: Text(_currentStep < _totalSteps - 1 ? 'Next' : 'Generate Resume'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateStep() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose a Template',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a style that best fits your industry and personal preference',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildTemplateCard(
                  'Professional',
                  'Clean and traditional format',
                  Icons.business_center_outlined,
                  Colors.blue,
                  ResumeStyle.professional,
                ),
                _buildTemplateCard(
                  'Creative',
                  'Eye-catching design',
                  Icons.palette_outlined,
                  Colors.purple,
                  ResumeStyle.creative,
                ),
                _buildTemplateCard(
                  'Minimalist',
                  'Simple and elegant',
                  Icons.minimize_outlined,
                  Colors.grey,
                  ResumeStyle.minimalist,
                ),
                _buildTemplateCard(
                  'Modern',
                  'Contemporary design',
                  Icons.auto_awesome_outlined,
                  Colors.teal,
                  ResumeStyle.modern,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(String title, String description, IconData icon, Color color, ResumeStyle style) {
    final isSelected = _selectedStyle == style;

    return Card(
      elevation: isSelected ? 4 : 2,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedStyle = style;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isSelected
                ? Border.all(color: color, width: 2)
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: isSelected ? 0.2 : 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 32, color: color),
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isSelected ? color : null,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                if (isSelected)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Icon(
                      Icons.check_circle,
                      color: color,
                      size: 20,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalInfoStep() {
    return PersonalInfoForm(
      initialData: _personalInfo,
      onChanged: (personalInfo) {
        setState(() {
          _personalInfo = personalInfo;
        });
      },
      onAIAssistRequested: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AIAssistantScreen(
              initialContext: AIAssistantContext.resumeCreation,
              initialContextData: {
                'step': 'personal_info',
                'help_type': 'personal_information',
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryStep() {
    return SummaryForm(
      initialData: _summary,
      onChanged: (summary) {
        setState(() {
          _summary = summary;
        });
      },
      onAIAssistRequested: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AIAssistantScreen(
              initialContext: AIAssistantContext.resumeCreation,
              initialContextData: {
                'step': 'summary',
                'help_type': 'professional_summary',
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildExperienceStep() {
    return WorkExperienceForm(
      initialData: _workExperience,
      onChanged: (experiences) {
        setState(() {
          _workExperience = experiences;
        });
      },
      onAIAssistRequested: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AIAssistantScreen(
              initialContext: AIAssistantContext.jobDescriptionHelp,
              initialContextData: {
                'step': 'work_experience',
                'help_type': 'job_descriptions',
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildEducationAndSkillsStep() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
            ),
            child: TabBar(
              tabs: const [
                Tab(text: 'Education'),
                Tab(text: 'Skills'),
              ],
              labelColor: Theme.of(context).colorScheme.primary,
              unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
              indicator: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
              ),
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                EducationForm(
                  initialData: _education,
                  onChanged: (education) {
                    setState(() {
                      _education = education;
                    });
                  },
                  onAIAssistRequested: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AIAssistantScreen(
                          initialContext: AIAssistantContext.resumeCreation,
                          initialContextData: {
                            'step': 'education',
                            'help_type': 'education_details',
                          },
                        ),
                      ),
                    );
                  },
                ),
                SkillsForm(
                  initialData: _skills,
                  onChanged: (skills) {
                    setState(() {
                      _skills = skills;
                    });
                  },
                  onAIAssistRequested: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const AIAssistantScreen(
                          initialContext: AIAssistantContext.resumeCreation,
                          initialContextData: {
                            'step': 'skills',
                            'help_type': 'skill_suggestions',
                          },
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    return ReviewStep(
      personalInfo: _personalInfo,
      summary: _summary,
      workExperience: _workExperience,
      education: _education,
      skills: _skills,
      selectedStyle: _selectedStyle,
      onGeneratePressed: _generateResume,
      isGenerating: _isGenerating,
    );
  }

  Future<void> _generateResume() async {
    if (_isGenerating) return;

    // Validate required fields
    if (!_validateResumeData()) {
      return;
    }

    setState(() {
      _isGenerating = true;
    });

    try {
      // Get current user
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Create resume model
      final resume = ResumeModel(
        id: const Uuid().v4(),
        userId: currentUser.uid,
        name: '${_personalInfo.fullName} Resume',
        personalInfo: _personalInfo,
        summary: _summary,
        workExperience: _workExperience,
        education: _education,
        skills: _skills,
        achievements: [], // Can be added later
        style: _selectedStyle.toString().split('.').last,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        version: 1,
      );

      // Generate PDF
      final pdfBytes = await _pdfService.generateResumePdf(
        resume: resume,
        style: _selectedStyle,
      );

      // Upload PDF to storage
      final pdfUrl = await _storageService.uploadResumePdf(
        userId: currentUser.uid,
        resumeId: resume.id,
        pdfBytes: pdfBytes,
        fileName: '${_personalInfo.fullName.replaceAll(' ', '_')}_resume.pdf',
      );

      // Save resume to Firestore
      final updatedResume = resume.copyWith(pdfUrl: pdfUrl);
      await _firestoreService.createResume(updatedResume);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Resume generated successfully!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            action: SnackBarAction(
              label: 'View',
              textColor: Theme.of(context).colorScheme.onPrimary,
              onPressed: () {
                // TODO: Navigate to resume view or download
              },
            ),
          ),
        );

        // Navigate back or to resume list
        Navigator.of(context).pop(updatedResume);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating resume: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  bool _validateResumeData() {
    // Check required personal info
    if (_personalInfo.firstName.isEmpty ||
        _personalInfo.lastName.isEmpty ||
        _personalInfo.email.isEmpty ||
        _personalInfo.phone.isEmpty) {
      _showValidationError('Please complete all required personal information fields.');
      return false;
    }

    // Check email format
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(_personalInfo.email)) {
      _showValidationError('Please enter a valid email address.');
      return false;
    }

    // Check if at least one section has content
    if (_summary.isEmpty && _workExperience.isEmpty && _education.isEmpty && _skills.isEmpty) {
      _showValidationError('Please add content to at least one section (Summary, Experience, Education, or Skills).');
      return false;
    }

    return true;
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
