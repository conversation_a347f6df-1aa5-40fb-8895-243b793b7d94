import 'package:flutter/material.dart';
import '../../widgets/common/ai_diagnostic_widget.dart';
import '../../utils/design_tokens.dart';

class AIDiagnosticScreen extends StatelessWidget {
  const AIDiagnosticScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Assistant Diagnostics'),
        backgroundColor: DesignTokens.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: DesignTokens.primaryBlue.withValues(alpha: 0.1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'AI Assistant Troubleshooting',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: DesignTokens.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Use this screen to diagnose and test AI assistant functionality. If you\'re experiencing issues with AI responses, run the tests below.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DesignTokens.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // Diagnostic Widget
            const AIDiagnosticWidget(),
            
            // Troubleshooting Tips
            _buildTroubleshootingTips(context),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildTroubleshootingTips(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Troubleshooting Tips',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.primaryBlue,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildTipItem(
              context,
              icon: Icons.wifi_off,
              title: 'No Response from AI',
              description: 'Check your internet connection and try again. The AI service requires a stable internet connection.',
            ),
            
            _buildTipItem(
              context,
              icon: Icons.access_time,
              title: 'Slow Responses',
              description: 'AI responses may take 10-30 seconds. Please be patient and avoid sending multiple requests.',
            ),
            
            _buildTipItem(
              context,
              icon: Icons.error_outline,
              title: 'Service Errors',
              description: 'If you see initialization errors, the AI service may be temporarily unavailable. Try restarting the app.',
            ),
            
            _buildTipItem(
              context,
              icon: Icons.keyboard,
              title: 'Input Issues',
              description: 'If the keyboard is not responding properly, try closing and reopening the chat screen.',
            ),
            
            _buildTipItem(
              context,
              icon: Icons.refresh,
              title: 'General Issues',
              description: 'For persistent issues, try force-closing and restarting the app, or check for app updates.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: DesignTokens.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: DesignTokens.primaryBlue,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: DesignTokens.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
