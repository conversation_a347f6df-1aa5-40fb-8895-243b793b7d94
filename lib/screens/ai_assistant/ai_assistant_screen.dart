import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/ai_assistant/ai_assistant_bloc.dart';
import '../../models/ai_chat_model.dart';
import '../../widgets/ai_assistant/chat_message_widget.dart';
import '../../widgets/ai_assistant/context_selector.dart';
import '../../widgets/ai_assistant/quick_actions_widget.dart';
import '../../utils/design_tokens.dart';

class AIAssistantScreen extends StatefulWidget {
  final AIAssistantContext? initialContext;
  final Map<String, dynamic>? initialContextData;

  const AIAssistantScreen({
    super.key,
    this.initialContext,
    this.initialContextData,
  });

  @override
  State<AIAssistantScreen> createState() => _AIAssistantScreenState();
}

class _AIAssistantScreenState extends State<AIAssistantScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();
  
  AIAssistantContext _currentContext = AIAssistantContext.general;

  @override
  void initState() {
    super.initState();
    if (widget.initialContext != null) {
      _currentContext = widget.initialContext!;
      context.read<AIAssistantBloc>().add(
        AIAssistantContextChanged(
          context: _currentContext,
          additionalContext: widget.initialContextData,
        ),
      );
    }
    context.read<AIAssistantBloc>().add(AIAssistantStarted());
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    if (kDebugMode) {
      print('📝 Sending message: $message');
      print('🎯 Context: ${_currentContext.name}');
    }

    context.read<AIAssistantBloc>().add(
      AIAssistantMessageSent(
        message: message,
        context: _currentContext,
        additionalContext: widget.initialContextData,
      ),
    );

    _messageController.clear();
    _messageFocusNode.requestFocus();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _testAIConnection() {
    context.read<AIAssistantBloc>().add(
      AIAssistantMessageSent(
        message: 'Hello, please respond with "AI is working correctly" to test the connection.',
        context: AIAssistantContext.general,
        additionalContext: {'test': true},
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Assistant'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report_outlined),
            onPressed: () {
              _testAIConnection();
            },
            tooltip: 'Test AI Connection',
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () {
              _showContextSelector();
            },
          ),
        ],
      ),
      body: BlocConsumer<AIAssistantBloc, AIAssistantState>(
        listener: (context, state) {
          if (state is AIAssistantMessageReceived) {
            _scrollToBottom();
          } else if (state is AIAssistantMessageSending) {
            _scrollToBottom();
          } else if (state is AIAssistantError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('AI Assistant Error'),
                    Text(
                      state.message,
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                backgroundColor: Theme.of(context).colorScheme.error,
                duration: Duration(seconds: 8),
                action: SnackBarAction(
                  label: 'Retry',
                  textColor: Colors.white,
                  onPressed: () {
                    // Retry the last message
                    if (state.conversation != null && state.conversation!.messages.isNotEmpty) {
                      final lastUserMessage = state.conversation!.messages
                          .where((msg) => msg.type == MessageType.user)
                          .lastOrNull;
                      if (lastUserMessage != null) {
                        context.read<AIAssistantBloc>().add(
                          AIAssistantMessageSent(
                            message: lastUserMessage.content,
                            context: _currentContext,
                            additionalContext: widget.initialContextData,
                          ),
                        );
                      }
                    }
                  },
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          // Extract messages and loading state from the current state
          List<ChatMessage> messages = [];
          bool isLoading = false;

          if (kDebugMode) {
            print('🎯 AI Assistant Screen State: ${state.runtimeType}');
          }

          if (state is AIAssistantMessageReceived) {
            messages = state.conversation.messages;
            isLoading = false;
            if (kDebugMode) {
              print('📨 Message received, total messages: ${messages.length}');
            }
          } else if (state is AIAssistantMessageSending) {
            messages = state.conversation.messages;
            isLoading = true;
            if (kDebugMode) {
              print('📤 Message sending, total messages: ${messages.length}');
            }
          } else if (state is AIAssistantLoaded && state.currentConversation != null) {
            messages = state.currentConversation!.messages;
            isLoading = false;
            if (kDebugMode) {
              print('✅ Loaded state, total messages: ${messages.length}');
            }
          } else if (state is AIAssistantError && state.conversation != null) {
            messages = state.conversation!.messages;
            isLoading = false;
            if (kDebugMode) {
              print('❌ Error state, total messages: ${messages.length}');
            }
          } else {
            if (kDebugMode) {
              print('🔄 Initial or unknown state: ${state.runtimeType}');
            }
          }

          return Column(
          children: [
            // Context indicator
            if (_currentContext != AIAssistantContext.general)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                color: Theme.of(context).colorScheme.primaryContainer,
                child: Row(
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      size: 16,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Context: ${_currentContext.displayName}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: _showContextSelector,
                      child: Text(
                        'Change',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Messages list
            Expanded(
              child: messages.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: messages.length,
                      itemBuilder: (context, index) {
                        final message = messages[index];
                        return ChatMessageWidget(
                          message: message,
                          isLoading: isLoading && index == messages.length - 1,
                        );
                      },
                    ),
            ),

            // Quick actions (when no messages)
            if (messages.isEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: QuickActionsWidget(
                  context: _currentContext,
                  onActionTapped: (action) {
                    _messageController.text = action;
                    _sendMessage();
                  },
                ),
              ),

            // Message input
            _buildMessageInput(isLoading),
          ],
        );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.auto_awesome,
                size: 48,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'AI Resume Assistant',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'I\'m here to help you create an amazing resume! Ask me anything about resume writing, job descriptions, or career advice.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput(bool isLoading) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              focusNode: _messageFocusNode,
              decoration: InputDecoration(
                hintText: 'Ask me anything about resumes...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
              enabled: !isLoading,
            ),
          ),
          const SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
            ),
            child: IconButton(
              onPressed: isLoading ? null : _sendMessage,
              icon: isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.send_rounded,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void _showContextSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ContextSelector(
        currentContext: _currentContext,
        onContextSelected: (context) {
          setState(() {
            _currentContext = context;
          });
          this.context.read<AIAssistantBloc>().add(
            AIAssistantContextChanged(
              context: context,
              additionalContext: widget.initialContextData,
            ),
          );
          Navigator.pop(this.context);
        },
      ),
    );
  }
}
