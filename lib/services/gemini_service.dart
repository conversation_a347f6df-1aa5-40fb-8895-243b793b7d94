import 'package:firebase_ai/firebase_ai.dart';
import 'package:flutter/foundation.dart';
import '../models/resume_model.dart';
import '../models/work_experience_model.dart';
import '../models/education_model.dart';
import '../models/skill_model.dart';

class GeminiService {
  late final GenerativeModel _model;

  GeminiService() {
    try {
      _model = FirebaseAI.googleAI().generativeModel(model: 'gemini-1.5-flash');
      if (kDebugMode) {
        print('✅ Gemini Service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Gemini Service: $e');
      }
      rethrow;
    }
  }

  Future<String> generateResumeContent({
    required ResumeModel resume,
    required ResumeStyle style,
    String? targetIndustry,
    String? targetJobTitle,
  }) async {              
    try {
      final prompt = _buildPrompt(
        resume: resume,
        style: style,
        targetIndustry: targetIndustry,
        targetJobTitle: targetJobTitle,
      );

      final response = await _callGeminiAPI(prompt);
      return response;
    } catch (e) {
      throw Exception('Error generating resume content: $e');
    }
  }

  Future<String> generateSummary({
    required PersonalInfo personalInfo,
    required List<WorkExperience> workExperience,
    required List<Education> education,
    required List<Skill> skills,
    String? targetIndustry,
    String? targetJobTitle,
  }) async {
    try {
      final prompt = _buildSummaryPrompt(
        personalInfo: personalInfo,
        workExperience: workExperience,
        education: education,
        skills: skills,
        targetIndustry: targetIndustry,
        targetJobTitle: targetJobTitle,
      );

      final response = await _callGeminiAPI(prompt);
      return response;
    } catch (e) {
      throw Exception('Error generating summary: $e');
    }
  }

  Future<List<String>> generateJobDescriptionBullets({
    required String jobTitle,
    required String company,
    required String industry,
    required List<String> currentResponsibilities,
  }) async {
    try {
      final prompt = _buildJobDescriptionPrompt(
        jobTitle: jobTitle,
        company: company,
        industry: industry,
        currentResponsibilities: currentResponsibilities,
      );

      final response = await _callGeminiAPI(prompt);
      return _parseListResponse(response);
    } catch (e) {
      throw Exception('Error generating job description bullets: $e');
    }
  }

  Future<List<String>> generateSkillSuggestions({
    required String industry,
    required String jobTitle,
    required List<Skill> currentSkills,
  }) async {
    try {
      final prompt = _buildSkillSuggestionsPrompt(
        industry: industry,
        jobTitle: jobTitle,
        currentSkills: currentSkills,
      );

      final response = await _callGeminiAPI(prompt);
      return _parseListResponse(response);
    } catch (e) {
      throw Exception('Error generating skill suggestions: $e');
    }
  }

  Future<String> optimizeForATS({
    required String content,
    required String jobDescription,
  }) async {
    try {
      final prompt = _buildATSOptimizationPrompt(
        content: content,
        jobDescription: jobDescription,
      );

      final response = await _callGeminiAPI(prompt);
      return response;
    } catch (e) {
      throw Exception('Error optimizing for ATS: $e');
    }
  }

  Future<String> _callGeminiAPI(String prompt) async {
    try {
      if (kDebugMode) {
        print('🔧 Gemini API: Making request with prompt length: ${prompt.length}');
      }

      final content = [Content.text(prompt)];
      final response = await _model.generateContent(
        content,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      );

      if (kDebugMode) {
        print('📨 Gemini API Response: ${response.text?.isNotEmpty == true ? 'Success' : 'Empty'}');
      }

      if (response.text != null && response.text!.isNotEmpty) {
        return response.text!;
      } else {
        throw Exception('No content generated - empty response from Gemini API');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Gemini API Error: $e');
      }
      throw Exception('API call failed: $e');
    }
  }

  String _buildPrompt({
    required ResumeModel resume,
    required ResumeStyle style,
    String? targetIndustry,
    String? targetJobTitle,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Create a professional, ATS-friendly resume in ${style.displayName} style.');
    buffer.writeln('Style description: ${style.description}');
    
    if (targetIndustry != null) {
      buffer.writeln('Target Industry: $targetIndustry');
    }
    if (targetJobTitle != null) {
      buffer.writeln('Target Job Title: $targetJobTitle');
    }
    
    buffer.writeln('\nPersonal Information:');
    buffer.writeln('Name: ${resume.personalInfo.fullName}');
    buffer.writeln('Email: ${resume.personalInfo.email}');
    buffer.writeln('Phone: ${resume.personalInfo.phone}');
    if (resume.personalInfo.linkedIn != null) {
      buffer.writeln('LinkedIn: ${resume.personalInfo.linkedIn}');
    }
    
    buffer.writeln('\nCurrent Summary:');
    buffer.writeln(resume.summary);
    
    buffer.writeln('\nWork Experience:');
    for (final exp in resume.workExperience) {
      buffer.writeln('${exp.jobTitle} at ${exp.company} (${exp.dateRange})');
      buffer.writeln('Description: ${exp.description}');
      if (exp.responsibilities.isNotEmpty) {
        buffer.writeln('Responsibilities:');
        for (final resp in exp.responsibilities) {
          buffer.writeln('• $resp');
        }
      }
    }
    
    buffer.writeln('\nEducation:');
    for (final edu in resume.education) {
      buffer.writeln('${edu.fullDegree} from ${edu.institution} (${edu.dateRange})');
    }
    
    buffer.writeln('\nSkills:');
    for (final skill in resume.skills) {
      buffer.writeln('${skill.name} (${skill.level.displayName})');
    }
    
    buffer.writeln('\nPlease generate an improved, ATS-optimized resume content that:');
    buffer.writeln('1. Uses strong action verbs and quantifiable achievements');
    buffer.writeln('2. Includes relevant keywords for the target industry/role');
    buffer.writeln('3. Follows the specified style guidelines');
    buffer.writeln('4. Is formatted for easy parsing by ATS systems');
    buffer.writeln('5. Highlights the most relevant experience and skills');
    
    return buffer.toString();
  }

  String _buildSummaryPrompt({
    required PersonalInfo personalInfo,
    required List<WorkExperience> workExperience,
    required List<Education> education,
    required List<Skill> skills,
    String? targetIndustry,
    String? targetJobTitle,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Generate a compelling professional summary for:');
    buffer.writeln('Name: ${personalInfo.fullName}');
    
    if (targetIndustry != null) {
      buffer.writeln('Target Industry: $targetIndustry');
    }
    if (targetJobTitle != null) {
      buffer.writeln('Target Position: $targetJobTitle');
    }
    
    buffer.writeln('\nWork Experience:');
    for (final exp in workExperience) {
      buffer.writeln('${exp.jobTitle} at ${exp.company} (${exp.duration})');
    }
    
    buffer.writeln('\nEducation:');
    for (final edu in education) {
      buffer.writeln('${edu.fullDegree} from ${edu.institution}');
    }
    
    buffer.writeln('\nKey Skills:');
    for (final skill in skills.take(10)) {
      buffer.writeln(skill.name);
    }
    
    buffer.writeln('\nCreate a 3-4 sentence professional summary that:');
    buffer.writeln('1. Highlights years of experience and key expertise');
    buffer.writeln('2. Mentions relevant skills and achievements');
    buffer.writeln('3. Aligns with the target industry/position');
    buffer.writeln('4. Uses strong, professional language');
    
    return buffer.toString();
  }

  String _buildJobDescriptionPrompt({
    required String jobTitle,
    required String company,
    required String industry,
    required List<String> currentResponsibilities,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Improve these job responsibilities for a $jobTitle position at $company in the $industry industry:');
    buffer.writeln('\nCurrent responsibilities:');
    for (final resp in currentResponsibilities) {
      buffer.writeln('• $resp');
    }
    
    buffer.writeln('\nGenerate 5-7 improved bullet points that:');
    buffer.writeln('1. Start with strong action verbs');
    buffer.writeln('2. Include quantifiable achievements where possible');
    buffer.writeln('3. Use industry-relevant keywords');
    buffer.writeln('4. Demonstrate impact and results');
    buffer.writeln('5. Are concise and ATS-friendly');
    
    return buffer.toString();
  }

  String _buildSkillSuggestionsPrompt({
    required String industry,
    required String jobTitle,
    required List<Skill> currentSkills,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Suggest additional relevant skills for a $jobTitle in the $industry industry.');
    buffer.writeln('\nCurrent skills:');
    for (final skill in currentSkills) {
      buffer.writeln('• ${skill.name}');
    }
    
    buffer.writeln('\nSuggest 8-10 additional skills that would be valuable for this role.');
    buffer.writeln('Focus on:');
    buffer.writeln('1. Technical skills relevant to the industry');
    buffer.writeln('2. Software and tools commonly used');
    buffer.writeln('3. Certifications that would be beneficial');
    buffer.writeln('4. Soft skills important for the role');
    
    return buffer.toString();
  }

  String _buildATSOptimizationPrompt({
    required String content,
    required String jobDescription,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Optimize this resume content for ATS systems based on this job description:');
    buffer.writeln('\nJob Description:');
    buffer.writeln(jobDescription);
    buffer.writeln('\nCurrent Resume Content:');
    buffer.writeln(content);
    
    buffer.writeln('\nOptimize the resume to:');
    buffer.writeln('1. Include relevant keywords from the job description');
    buffer.writeln('2. Match required skills and qualifications');
    buffer.writeln('3. Use ATS-friendly formatting');
    buffer.writeln('4. Maintain readability and professional tone');
    
    return buffer.toString();
  }

  List<String> _parseListResponse(String response) {
    final lines = response.split('\n');
    final bullets = <String>[];
    
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isNotEmpty && (trimmed.startsWith('•') || trimmed.startsWith('-') || trimmed.startsWith('*'))) {
        bullets.add(trimmed.substring(1).trim());
      } else if (trimmed.isNotEmpty && RegExp(r'^\d+\.').hasMatch(trimmed)) {
        bullets.add(trimmed.replaceFirst(RegExp(r'^\d+\.\s*'), ''));
      }
    }
    
    return bullets;
  }
}
