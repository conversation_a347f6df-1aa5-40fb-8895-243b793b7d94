import 'package:firebase_ai/firebase_ai.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/ai_chat_model.dart';
import '../models/resume_model.dart';
import '../models/work_experience_model.dart';
import '../models/education_model.dart';
import '../models/skill_model.dart';

class AIAssistantService {
  late final GenerativeModel _model;
  bool _isInitialized = false;
  String? _initializationError;

  AIAssistantService() {
    _initializeService();
  }

  bool get isInitialized => _isInitialized;
  String? get initializationError => _initializationError;

  void _initializeService() {
    try {
      if (kDebugMode) {
        print('🔄 Initializing AI Assistant Service...');
      }

      _model = FirebaseAI.googleAI().generativeModel(model: 'gemini-1.5-flash');
      _isInitialized = true;
      _initializationError = null;

      if (kDebugMode) {
        print('✅ AI Assistant Service initialized successfully');
        print('📋 Model: gemini-1.5-flash');
      }
    } catch (e) {
      _isInitialized = false;
      _initializationError = e.toString();

      if (kDebugMode) {
        print('❌ Failed to initialize AI Assistant Service: $e');
        print('🔧 Possible solutions:');
        print('   1. Check Firebase AI configuration');
        print('   2. Verify API key is set correctly');
        print('   3. Ensure Generative AI API is enabled');
        print('   4. Check internet connectivity');
      }
      rethrow;
    }
  }

  Future<bool> testConnection() async {
    try {
      if (!_isInitialized) {
        throw Exception('Service not initialized: $_initializationError');
      }

      if (kDebugMode) {
        print('🧪 Testing AI connection...');
      }

      final content = [Content.text('Hello, respond with "Connection test successful"')];
      final response = await _model.generateContent(
        content,
        generationConfig: GenerationConfig(
          temperature: 0.1,
          maxOutputTokens: 20,
        ),
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('Connection test timed out', const Duration(seconds: 10)),
      );

      final success = response.text != null && response.text!.isNotEmpty;

      if (kDebugMode) {
        if (success) {
          print('✅ AI connection test successful');
          print('📝 Response: ${response.text}');
        } else {
          print('❌ AI connection test failed: Empty response');
        }
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ AI connection test failed: $e');
      }
      return false;
    }
  }

  Future<String> sendMessage({
    required String message,
    required AIAssistantContext context,
    List<ChatMessage>? conversationHistory,
    Map<String, dynamic>? additionalContext,
    Duration? timeout,
  }) async {
    try {
      // Check if service is initialized
      if (!_isInitialized) {
        throw Exception('AI Assistant Service not initialized: $_initializationError');
      }

      // Validate input
      if (message.trim().isEmpty) {
        throw Exception('Message cannot be empty');
      }

      if (kDebugMode) {
        print('🤖 AI Assistant: Sending message with context: ${context.name}');
        print('📝 Message: ${message.length > 100 ? '${message.substring(0, 100)}...' : message}');
        print('⏱️ Timeout: ${timeout?.inSeconds ?? 30} seconds');
      }

      final prompt = _buildPrompt(
        message: message,
        context: context,
        conversationHistory: conversationHistory,
        additionalContext: additionalContext,
      );

      if (kDebugMode) {
        print('🔧 Generated prompt length: ${prompt.length}');
      }

      final content = [Content.text(prompt)];

      // Add timeout to the request
      final response = await _model.generateContent(
        content,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        ),
      ).timeout(
        timeout ?? const Duration(seconds: 30),
        onTimeout: () {
          if (kDebugMode) {
            print('⏰ AI request timed out after ${timeout?.inSeconds ?? 30} seconds');
          }
          throw TimeoutException(
            'AI request timed out. Please check your internet connection and try again.',
            timeout ?? const Duration(seconds: 30),
          );
        },
      );

      if (kDebugMode) {
        print('📨 AI Response received: ${response.text?.isNotEmpty == true ? 'Success' : 'Empty'}');
        if (response.text != null) {
          print('📏 Response length: ${response.text!.length} characters');
        }
      }

      if (response.text != null && response.text!.isNotEmpty) {
        return response.text!.trim();
      } else {
        throw Exception('No response generated - empty response from AI model. This might be due to content filtering or API issues.');
      }
    } on TimeoutException catch (e) {
      if (kDebugMode) {
        print('⏰ AI Assistant Timeout: $e');
      }
      rethrow;
    } catch (e) {
      if (kDebugMode) {
        print('❌ AI Assistant Error: $e');
        print('🔍 Error type: ${e.runtimeType}');

        // Provide specific error guidance
        if (e.toString().contains('API key')) {
          print('💡 Suggestion: Check your Firebase AI API key configuration');
        } else if (e.toString().contains('quota') || e.toString().contains('limit')) {
          print('💡 Suggestion: You may have exceeded API quota limits');
        } else if (e.toString().contains('network') || e.toString().contains('connection')) {
          print('💡 Suggestion: Check your internet connection');
        }
      }
      throw Exception('Failed to get AI response: $e');
    }
  }

  Future<List<String>> generateJobDescriptionBullets({
    required String jobTitle,
    required String company,
    required String industry,
    List<String>? currentResponsibilities,
    String? additionalContext,
  }) async {
    try {
      final prompt = _buildJobDescriptionPrompt(
        jobTitle: jobTitle,
        company: company,
        industry: industry,
        currentResponsibilities: currentResponsibilities ?? [],
        additionalContext: additionalContext,
      );

      final response = await sendMessage(
        message: prompt,
        context: AIAssistantContext.jobDescriptionHelp,
      );

      return _parseListResponse(response);
    } catch (e) {
      throw Exception('Failed to generate job description bullets: $e');
    }
  }

  Future<List<String>> generateSkillSuggestions({
    required String industry,
    required String jobTitle,
    List<Skill>? currentSkills,
    int? experienceYears,
  }) async {
    try {
      final prompt = _buildSkillSuggestionsPrompt(
        industry: industry,
        jobTitle: jobTitle,
        currentSkills: currentSkills ?? [],
        experienceYears: experienceYears,
      );

      final response = await sendMessage(
        message: prompt,
        context: AIAssistantContext.skillSuggestions,
      );

      return _parseListResponse(response);
    } catch (e) {
      throw Exception('Failed to generate skill suggestions: $e');
    }
  }

  Future<String> generateProfessionalSummary({
    required PersonalInfo personalInfo,
    required List<WorkExperience> workExperience,
    required List<Education> education,
    required List<Skill> skills,
    String? targetIndustry,
    String? targetJobTitle,
  }) async {
    try {
      final prompt = _buildSummaryPrompt(
        personalInfo: personalInfo,
        workExperience: workExperience,
        education: education,
        skills: skills,
        targetIndustry: targetIndustry,
        targetJobTitle: targetJobTitle,
      );

      return await sendMessage(
        message: prompt,
        context: AIAssistantContext.summaryGeneration,
      );
    } catch (e) {
      throw Exception('Failed to generate professional summary: $e');
    }
  }

  Future<String> optimizeForATS({
    required String content,
    required String jobDescription,
    String? targetKeywords,
  }) async {
    try {
      final prompt = _buildATSOptimizationPrompt(
        content: content,
        jobDescription: jobDescription,
        targetKeywords: targetKeywords,
      );

      return await sendMessage(
        message: prompt,
        context: AIAssistantContext.atsOptimization,
      );
    } catch (e) {
      throw Exception('Failed to optimize for ATS: $e');
    }
  }

  Future<List<String>> getResumeImprovementSuggestions({
    required ResumeModel resume,
    String? targetJobDescription,
  }) async {
    try {
      final prompt = _buildImprovementSuggestionsPrompt(
        resume: resume,
        targetJobDescription: targetJobDescription,
      );

      final response = await sendMessage(
        message: prompt,
        context: AIAssistantContext.general,
      );

      return _parseListResponse(response);
    } catch (e) {
      throw Exception('Failed to get improvement suggestions: $e');
    }
  }

  String _buildPrompt({
    required String message,
    required AIAssistantContext context,
    List<ChatMessage>? conversationHistory,
    Map<String, dynamic>? additionalContext,
  }) {
    final buffer = StringBuffer();
    
    // Add system prompt based on context
    buffer.writeln(context.systemPrompt);
    buffer.writeln();
    
    // Add conversation history if available
    if (conversationHistory != null && conversationHistory.isNotEmpty) {
      buffer.writeln('Previous conversation:');
      for (final msg in conversationHistory.take(10)) { // Limit to last 10 messages
        final role = msg.type == MessageType.user ? 'User' : 'Assistant';
        buffer.writeln('$role: ${msg.content}');
      }
      buffer.writeln();
    }
    
    // Add additional context if provided
    if (additionalContext != null && additionalContext.isNotEmpty) {
      buffer.writeln('Additional context:');
      additionalContext.forEach((key, value) {
        buffer.writeln('$key: $value');
      });
      buffer.writeln();
    }
    
    // Add the current message
    buffer.writeln('User: $message');
    buffer.writeln();
    buffer.writeln('Please provide a helpful, specific, and actionable response.');
    
    return buffer.toString();
  }

  String _buildJobDescriptionPrompt({
    required String jobTitle,
    required String company,
    required String industry,
    required List<String> currentResponsibilities,
    String? additionalContext,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Help me improve job description bullets for:');
    buffer.writeln('Position: $jobTitle');
    buffer.writeln('Company: $company');
    buffer.writeln('Industry: $industry');
    
    if (additionalContext != null) {
      buffer.writeln('Additional context: $additionalContext');
    }
    
    if (currentResponsibilities.isNotEmpty) {
      buffer.writeln('\nCurrent responsibilities:');
      for (final resp in currentResponsibilities) {
        buffer.writeln('• $resp');
      }
    }
    
    buffer.writeln('\nGenerate 5-7 improved bullet points that:');
    buffer.writeln('1. Start with strong action verbs');
    buffer.writeln('2. Include quantifiable achievements where possible');
    buffer.writeln('3. Use industry-relevant keywords');
    buffer.writeln('4. Demonstrate impact and results');
    buffer.writeln('5. Are concise and ATS-friendly');
    buffer.writeln('\nFormat each bullet point on a new line starting with "•"');
    
    return buffer.toString();
  }

  String _buildSkillSuggestionsPrompt({
    required String industry,
    required String jobTitle,
    required List<Skill> currentSkills,
    int? experienceYears,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Suggest relevant skills for:');
    buffer.writeln('Position: $jobTitle');
    buffer.writeln('Industry: $industry');
    
    if (experienceYears != null) {
      buffer.writeln('Experience level: $experienceYears years');
    }
    
    if (currentSkills.isNotEmpty) {
      buffer.writeln('\nCurrent skills:');
      for (final skill in currentSkills) {
        buffer.writeln('• ${skill.name}');
      }
    }
    
    buffer.writeln('\nSuggest 8-12 additional relevant skills including:');
    buffer.writeln('1. Technical skills and tools');
    buffer.writeln('2. Software and platforms');
    buffer.writeln('3. Industry-specific skills');
    buffer.writeln('4. Relevant certifications');
    buffer.writeln('5. Important soft skills');
    buffer.writeln('\nFormat each skill on a new line starting with "•"');
    
    return buffer.toString();
  }

  String _buildSummaryPrompt({
    required PersonalInfo personalInfo,
    required List<WorkExperience> workExperience,
    required List<Education> education,
    required List<Skill> skills,
    String? targetIndustry,
    String? targetJobTitle,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Generate a compelling professional summary for:');
    buffer.writeln('Name: ${personalInfo.fullName}');
    
    if (targetIndustry != null) {
      buffer.writeln('Target Industry: $targetIndustry');
    }
    if (targetJobTitle != null) {
      buffer.writeln('Target Position: $targetJobTitle');
    }
    
    buffer.writeln('\nWork Experience:');
    for (final exp in workExperience.take(5)) {
      buffer.writeln('• ${exp.jobTitle} at ${exp.company}');
    }
    
    buffer.writeln('\nEducation:');
    for (final edu in education.take(3)) {
      buffer.writeln('• ${edu.degree} in ${edu.fieldOfStudy} from ${edu.institution}');
    }
    
    buffer.writeln('\nKey Skills:');
    for (final skill in skills.take(10)) {
      buffer.writeln('• ${skill.name}');
    }
    
    buffer.writeln('\nCreate a 3-4 sentence professional summary that:');
    buffer.writeln('1. Highlights years of experience and expertise');
    buffer.writeln('2. Mentions key achievements and skills');
    buffer.writeln('3. Aligns with target industry/position');
    buffer.writeln('4. Uses professional, impactful language');
    
    return buffer.toString();
  }

  String _buildATSOptimizationPrompt({
    required String content,
    required String jobDescription,
    String? targetKeywords,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Optimize this resume content for ATS systems:');
    buffer.writeln('\nJob Description:');
    buffer.writeln(jobDescription);
    
    if (targetKeywords != null) {
      buffer.writeln('\nTarget Keywords: $targetKeywords');
    }
    
    buffer.writeln('\nCurrent Content:');
    buffer.writeln(content);
    
    buffer.writeln('\nOptimize to:');
    buffer.writeln('1. Include relevant keywords from job description');
    buffer.writeln('2. Match required skills and qualifications');
    buffer.writeln('3. Use ATS-friendly formatting');
    buffer.writeln('4. Maintain readability and impact');
    
    return buffer.toString();
  }

  String _buildImprovementSuggestionsPrompt({
    required ResumeModel resume,
    String? targetJobDescription,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Analyze this resume and provide improvement suggestions:');
    buffer.writeln('\nResume Summary: ${resume.summary}');
    
    buffer.writeln('\nWork Experience:');
    for (final exp in resume.workExperience.take(3)) {
      buffer.writeln('• ${exp.jobTitle} at ${exp.company}');
      if (exp.responsibilities.isNotEmpty) {
        buffer.writeln('  Responsibilities: ${exp.responsibilities.take(2).join(', ')}');
      }
    }
    
    buffer.writeln('\nSkills: ${resume.skills.take(10).map((s) => s.name).join(', ')}');
    
    if (targetJobDescription != null) {
      buffer.writeln('\nTarget Job: $targetJobDescription');
    }
    
    buffer.writeln('\nProvide 5-8 specific improvement suggestions:');
    buffer.writeln('1. Content improvements');
    buffer.writeln('2. Keyword optimization');
    buffer.writeln('3. Structure enhancements');
    buffer.writeln('4. ATS optimization tips');
    buffer.writeln('\nFormat each suggestion on a new line starting with "•"');
    
    return buffer.toString();
  }

  List<String> _parseListResponse(String response) {
    final lines = response.split('\n');
    final items = <String>[];
    
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isNotEmpty && (trimmed.startsWith('•') || trimmed.startsWith('-') || trimmed.startsWith('*'))) {
        items.add(trimmed.substring(1).trim());
      } else if (trimmed.isNotEmpty && RegExp(r'^\d+\.').hasMatch(trimmed)) {
        items.add(trimmed.replaceFirst(RegExp(r'^\d+\.\s*'), ''));
      }
    }
    
    return items;
  }
}
