import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/resume_model.dart';
import '../models/work_experience_model.dart';
import '../models/education_model.dart';
import '../models/skill_model.dart';

class PdfGenerationService {
  static const double _margin = 40;
  static const double _sectionSpacing = 20;
  static const double _itemSpacing = 12;

  Future<Uint8List> generateResumePdf({
    required ResumeModel resume,
    required ResumeStyle style,
  }) async {
    final pdf = pw.Document();

    // Use default fonts
    final regularFont = pw.Font.helvetica();
    final boldFont = pw.Font.helveticaBold();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(_margin),
        build: (pw.Context context) {
          return [
            _buildHeader(resume.personalInfo, style, boldFont, regularFont),
            pw.SizedBox(height: _sectionSpacing),
            if (resume.summary.isNotEmpty) ...[
              _buildSummarySection(resume.summary, style, boldFont, regularFont),
              pw.SizedBox(height: _sectionSpacing),
            ],
            if (resume.workExperience.isNotEmpty) ...[
              _buildWorkExperienceSection(resume.workExperience, style, boldFont, regularFont),
              pw.SizedBox(height: _sectionSpacing),
            ],
            if (resume.education.isNotEmpty) ...[
              _buildEducationSection(resume.education, style, boldFont, regularFont),
              pw.SizedBox(height: _sectionSpacing),
            ],
            if (resume.skills.isNotEmpty) ...[
              _buildSkillsSection(resume.skills, style, boldFont, regularFont),
              pw.SizedBox(height: _sectionSpacing),
            ],
            if (resume.achievements.isNotEmpty) ...[
              _buildAchievementsSection(resume.achievements, style, boldFont, regularFont),
            ],
          ];
        },
      ),
    );

    return pdf.save();
  }



  pw.Widget _buildHeader(PersonalInfo personalInfo, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    final primaryColor = _getStyleColors(style)['primary']!;
    
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: _getStyleColors(style)['secondary']!,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            personalInfo.fullName,
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 28,
              color: primaryColor,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    if (personalInfo.email.isNotEmpty)
                      _buildContactItem('Email', personalInfo.email, regularFont),
                    if (personalInfo.phone.isNotEmpty)
                      _buildContactItem('Phone', personalInfo.phone, regularFont),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    if (personalInfo.linkedIn != null && personalInfo.linkedIn!.isNotEmpty)
                      _buildContactItem('LinkedIn', personalInfo.linkedIn!, regularFont),
                    if (personalInfo.website != null && personalInfo.website!.isNotEmpty)
                      _buildContactItem('Website', personalInfo.website!, regularFont),
                  ],
                ),
              ),
            ],
          ),
          if (_hasAddress(personalInfo)) ...[
            pw.SizedBox(height: 8),
            _buildContactItem('Address', _formatAddress(personalInfo), regularFont),
          ],
        ],
      ),
    );
  }

  pw.Widget _buildContactItem(String label, String value, pw.Font font) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 4),
      child: pw.RichText(
        text: pw.TextSpan(
          children: [
            pw.TextSpan(
              text: '$label: ',
              style: pw.TextStyle(font: font, fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
            pw.TextSpan(
              text: value,
              style: pw.TextStyle(font: font, fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }

  bool _hasAddress(PersonalInfo personalInfo) {
    return (personalInfo.address?.isNotEmpty ?? false) ||
           (personalInfo.city?.isNotEmpty ?? false) ||
           (personalInfo.state?.isNotEmpty ?? false) ||
           (personalInfo.zipCode?.isNotEmpty ?? false);
  }

  String _formatAddress(PersonalInfo personalInfo) {
    final parts = <String>[];
    if (personalInfo.address?.isNotEmpty ?? false) parts.add(personalInfo.address!);
    if (personalInfo.city?.isNotEmpty ?? false) parts.add(personalInfo.city!);
    if (personalInfo.state?.isNotEmpty ?? false) parts.add(personalInfo.state!);
    if (personalInfo.zipCode?.isNotEmpty ?? false) parts.add(personalInfo.zipCode!);
    return parts.join(', ');
  }

  pw.Widget _buildSectionHeader(String title, ResumeStyle style, pw.Font boldFont) {
    final primaryColor = _getStyleColors(style)['primary']!;
    
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 8),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(color: PdfColors.grey300, width: 1),
        ),
      ),
      child: pw.Text(
        title.toUpperCase(),
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 14,
          color: primaryColor,
          letterSpacing: 1,
        ),
      ),
    );
  }

  pw.Widget _buildSummarySection(String summary, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Professional Summary', style, boldFont),
        pw.SizedBox(height: 8),
        pw.Text(
          summary,
          style: pw.TextStyle(
            font: regularFont,
            fontSize: 11,
            lineSpacing: 1.4,
          ),
          textAlign: pw.TextAlign.justify,
        ),
      ],
    );
  }

  pw.Widget _buildWorkExperienceSection(List<WorkExperience> experiences, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Work Experience', style, boldFont),
        pw.SizedBox(height: 12),
        ...experiences.map((exp) => _buildWorkExperienceItem(exp, style, boldFont, regularFont)),
      ],
    );
  }

  pw.Widget _buildWorkExperienceItem(WorkExperience experience, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: _itemSpacing),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Text(
                  experience.jobTitle,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 12,
                  ),
                ),
              ),
              pw.Text(
                experience.dateRange,
                style: pw.TextStyle(
                  font: regularFont,
                  fontSize: 10,
                  color: PdfColors.grey600,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 2),
          pw.Row(
            children: [
              pw.Text(
                experience.company,
                style: pw.TextStyle(
                  font: regularFont,
                  fontSize: 11,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              if (experience.location?.isNotEmpty ?? false) ...[
                pw.Text(' • ', style: pw.TextStyle(font: regularFont, fontSize: 11)),
                pw.Text(
                  experience.location!,
                  style: pw.TextStyle(
                    font: regularFont,
                    fontSize: 11,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
            ],
          ),
          if (experience.description.isNotEmpty) ...[
            pw.SizedBox(height: 6),
            pw.Text(
              experience.description,
              style: pw.TextStyle(
                font: regularFont,
                fontSize: 10,
                lineSpacing: 1.3,
              ),
            ),
          ],
          if (experience.responsibilities.isNotEmpty) ...[
            pw.SizedBox(height: 6),
            ...experience.responsibilities.map((resp) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 2),
              child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('• ', style: pw.TextStyle(font: regularFont, fontSize: 10)),
                  pw.Expanded(
                    child: pw.Text(
                      resp,
                      style: pw.TextStyle(
                        font: regularFont,
                        fontSize: 10,
                        lineSpacing: 1.3,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  pw.Widget _buildEducationSection(List<Education> educations, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Education', style, boldFont),
        pw.SizedBox(height: 12),
        ...educations.map((edu) => _buildEducationItem(edu, style, boldFont, regularFont)),
      ],
    );
  }

  pw.Widget _buildEducationItem(Education education, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: _itemSpacing),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Expanded(
                child: pw.Text(
                  education.fullDegree,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 12,
                  ),
                ),
              ),
              pw.Text(
                education.dateRange,
                style: pw.TextStyle(
                  font: regularFont,
                  fontSize: 10,
                  color: PdfColors.grey600,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 2),
          pw.Row(
            children: [
              pw.Text(
                education.institution,
                style: pw.TextStyle(
                  font: regularFont,
                  fontSize: 11,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              if (education.location?.isNotEmpty ?? false) ...[
                pw.Text(' • ', style: pw.TextStyle(font: regularFont, fontSize: 11)),
                pw.Text(
                  education.location!,
                  style: pw.TextStyle(
                    font: regularFont,
                    fontSize: 11,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
              if (education.gpa != null) ...[
                pw.Text(' • ', style: pw.TextStyle(font: regularFont, fontSize: 11)),
                pw.Text(
                  education.gpaDisplay,
                  style: pw.TextStyle(
                    font: regularFont,
                    fontSize: 11,
                    color: PdfColors.grey600,
                  ),
                ),
              ],
            ],
          ),
          if (education.description?.isNotEmpty ?? false) ...[
            pw.SizedBox(height: 6),
            pw.Text(
              education.description!,
              style: pw.TextStyle(
                font: regularFont,
                fontSize: 10,
                lineSpacing: 1.3,
              ),
            ),
          ],
          if (education.coursework.isNotEmpty) ...[
            pw.SizedBox(height: 6),
            pw.Text(
              'Relevant Coursework: ${education.coursework.join(', ')}',
              style: pw.TextStyle(
                font: regularFont,
                fontSize: 10,
                color: PdfColors.grey600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  pw.Widget _buildSkillsSection(List<Skill> skills, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    // Group skills by category
    final skillsByCategory = <SkillCategory, List<Skill>>{};
    for (final skill in skills) {
      skillsByCategory.putIfAbsent(skill.category, () => []).add(skill);
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Skills', style, boldFont),
        pw.SizedBox(height: 12),
        ...skillsByCategory.entries.map((entry) =>
          _buildSkillCategoryItem(entry.key, entry.value, style, boldFont, regularFont)),
      ],
    );
  }

  pw.Widget _buildSkillCategoryItem(SkillCategory category, List<Skill> skills, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 8),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            category.displayName,
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 11,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Wrap(
            spacing: 8,
            runSpacing: 4,
            children: skills.map((skill) => pw.Container(
              padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: pw.BoxDecoration(
                color: _getStyleColors(style)['secondary']!,
                borderRadius: pw.BorderRadius.circular(12),
              ),
              child: pw.Text(
                '${skill.name} (${skill.level.displayName})',
                style: pw.TextStyle(
                  font: regularFont,
                  fontSize: 9,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  pw.Widget _buildAchievementsSection(List<String> achievements, ResumeStyle style, pw.Font boldFont, pw.Font regularFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Key Achievements', style, boldFont),
        pw.SizedBox(height: 12),
        ...achievements.map((achievement) => pw.Padding(
          padding: const pw.EdgeInsets.only(bottom: 6),
          child: pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('• ', style: pw.TextStyle(font: regularFont, fontSize: 10)),
              pw.Expanded(
                child: pw.Text(
                  achievement,
                  style: pw.TextStyle(
                    font: regularFont,
                    fontSize: 10,
                    lineSpacing: 1.3,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Map<String, PdfColor> _getStyleColors(ResumeStyle style) {
    switch (style) {
      case ResumeStyle.professional:
        return {
          'primary': PdfColors.blue800,
          'secondary': PdfColors.blue100,
        };
      case ResumeStyle.creative:
        return {
          'primary': PdfColors.purple800,
          'secondary': PdfColors.purple100,
        };
      case ResumeStyle.minimalist:
        return {
          'primary': PdfColors.grey800,
          'secondary': PdfColors.grey100,
        };
      case ResumeStyle.modern:
        return {
          'primary': PdfColors.teal800,
          'secondary': PdfColors.teal100,
        };
    }
  }
}
