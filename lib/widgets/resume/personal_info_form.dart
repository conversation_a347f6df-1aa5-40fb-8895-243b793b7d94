import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../models/resume_model.dart';
import '../../utils/design_tokens.dart';

class PersonalInfoForm extends StatefulWidget {
  final PersonalInfo? initialData;
  final Function(PersonalInfo) onChanged;
  final VoidCallback? onAIAssistRequested;

  const PersonalInfoForm({
    super.key,
    this.initialData,
    required this.onChanged,
    this.onAIAssistRequested,
  });

  @override
  State<PersonalInfoForm> createState() => _PersonalInfoFormState();
}

class _PersonalInfoFormState extends State<PersonalInfoForm> {
  final _formKey = GlobalKey<FormBuilderState>();
  late PersonalInfo _personalInfo;

  @override
  void initState() {
    super.initState();
    _personalInfo = widget.initialData ?? const PersonalInfo(
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
    );
  }

  void _updatePersonalInfo() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      
      _personalInfo = PersonalInfo(
        firstName: formData['firstName'] ?? '',
        lastName: formData['lastName'] ?? '',
        email: formData['email'] ?? '',
        phone: formData['phone'] ?? '',
        address: formData['address'],
        city: formData['city'],
        state: formData['state'],
        zipCode: formData['zipCode'],
        country: formData['country'],
        linkedIn: formData['linkedIn'],
        website: formData['website'],
        github: formData['github'],
      );
      
      widget.onChanged(_personalInfo);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: FormBuilder(
        key: _formKey,
        onChanged: _updatePersonalInfo,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Personal Information',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Let\'s start with your basic information',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.onAIAssistRequested != null)
                  IconButton.outlined(
                    onPressed: widget.onAIAssistRequested,
                    icon: const Icon(Icons.auto_awesome),
                    tooltip: 'Get AI help',
                  ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Basic Information Section
            _buildSection(
              title: 'Basic Information',
              icon: Icons.person_outline,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: FormBuilderTextField(
                        name: 'firstName',
                        initialValue: _personalInfo.firstName,
                        decoration: _buildInputDecoration('First Name'),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(),
                          FormBuilderValidators.minLength(2),
                        ]),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FormBuilderTextField(
                        name: 'lastName',
                        initialValue: _personalInfo.lastName,
                        decoration: _buildInputDecoration('Last Name'),
                        validator: FormBuilderValidators.compose([
                          FormBuilderValidators.required(),
                          FormBuilderValidators.minLength(2),
                        ]),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                FormBuilderTextField(
                  name: 'email',
                  initialValue: _personalInfo.email,
                  decoration: _buildInputDecoration('Email Address'),
                  keyboardType: TextInputType.emailAddress,
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(),
                    FormBuilderValidators.email(),
                  ]),
                ),
                const SizedBox(height: 16),
                FormBuilderTextField(
                  name: 'phone',
                  initialValue: _personalInfo.phone,
                  decoration: _buildInputDecoration('Phone Number'),
                  keyboardType: TextInputType.phone,
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(),
                  ]),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Address Section
            _buildSection(
              title: 'Address (Optional)',
              icon: Icons.location_on_outlined,
              children: [
                FormBuilderTextField(
                  name: 'address',
                  initialValue: _personalInfo.address,
                  decoration: _buildInputDecoration('Street Address'),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: FormBuilderTextField(
                        name: 'city',
                        initialValue: _personalInfo.city,
                        decoration: _buildInputDecoration('City'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FormBuilderTextField(
                        name: 'state',
                        initialValue: _personalInfo.state,
                        decoration: _buildInputDecoration('State'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FormBuilderTextField(
                        name: 'zipCode',
                        initialValue: _personalInfo.zipCode,
                        decoration: _buildInputDecoration('ZIP Code'),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                FormBuilderTextField(
                  name: 'country',
                  initialValue: _personalInfo.country,
                  decoration: _buildInputDecoration('Country'),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Online Presence Section
            _buildSection(
              title: 'Online Presence (Optional)',
              icon: Icons.link_outlined,
              children: [
                FormBuilderTextField(
                  name: 'linkedIn',
                  initialValue: _personalInfo.linkedIn,
                  decoration: _buildInputDecoration('LinkedIn Profile URL'),
                  keyboardType: TextInputType.url,
                ),
                const SizedBox(height: 16),
                FormBuilderTextField(
                  name: 'website',
                  initialValue: _personalInfo.website,
                  decoration: _buildInputDecoration('Personal Website'),
                  keyboardType: TextInputType.url,
                ),
                const SizedBox(height: 16),
                FormBuilderTextField(
                  name: 'github',
                  initialValue: _personalInfo.github,
                  decoration: _buildInputDecoration('GitHub Profile'),
                  keyboardType: TextInputType.url,
                ),
              ],
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    );
  }

  InputDecoration _buildInputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.primary,
          width: 2,
        ),
      ),
      filled: true,
      fillColor: Theme.of(context).colorScheme.surface,
    );
  }
}
