import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../utils/design_tokens.dart';

class SummaryForm extends StatefulWidget {
  final String initialData;
  final Function(String) onChanged;
  final VoidCallback? onAIAssistRequested;

  const SummaryForm({
    super.key,
    this.initialData = '',
    required this.onChanged,
    this.onAIAssistRequested,
  });

  @override
  State<SummaryForm> createState() => _SummaryFormState();
}

class _SummaryFormState extends State<SummaryForm> {
  final _formKey = GlobalKey<FormBuilderState>();
  final _controller = TextEditingController();
  late String _summary;

  @override
  void initState() {
    super.initState();
    _summary = widget.initialData;
    _controller.text = _summary;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _updateSummary() {
    final newSummary = _controller.text;
    if (newSummary != _summary) {
      _summary = newSummary;
      widget.onChanged(_summary);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Professional Summary',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Write a compelling summary that highlights your key qualifications',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.onAIAssistRequested != null)
                IconButton.outlined(
                  onPressed: widget.onAIAssistRequested,
                  icon: const Icon(Icons.auto_awesome),
                  tooltip: 'Generate with AI',
                ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Summary input section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                      ),
                      child: Icon(
                        Icons.description_outlined,
                        size: 20,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Summary',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                FormBuilder(
                  key: _formKey,
                  child: FormBuilderTextField(
                    name: 'summary',
                    controller: _controller,
                    decoration: InputDecoration(
                      labelText: 'Professional Summary',
                      hintText: 'Write a brief summary of your professional background, key skills, and career objectives. Keep it concise and impactful (2-4 sentences).',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor: Theme.of(context).colorScheme.surface,
                      alignLabelWithHint: true,
                    ),
                    maxLines: 6,
                    minLines: 4,
                    maxLength: 500,
                    validator: FormBuilderValidators.compose([
                      FormBuilderValidators.required(),
                      FormBuilderValidators.minLength(50, errorText: 'Summary should be at least 50 characters'),
                      FormBuilderValidators.maxLength(500, errorText: 'Summary should not exceed 500 characters'),
                    ]),
                    onChanged: (value) => _updateSummary(),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Character count
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Character count: ${_controller.text.length}/500',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (_controller.text.isNotEmpty)
                      Text(
                        'Word count: ${_controller.text.split(' ').where((word) => word.isNotEmpty).length}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Tips section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      size: 20,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Tips for a Great Summary',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                ..._buildTips(context),
              ],
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  List<Widget> _buildTips(BuildContext context) {
    final tips = [
      'Start with your years of experience and area of expertise',
      'Highlight 2-3 key skills or achievements',
      'Mention your career goals or what you\'re looking for',
      'Use action words and quantify achievements when possible',
      'Keep it concise - aim for 2-4 sentences',
      'Tailor it to the specific role or industry you\'re targeting',
    ];

    return tips.map((tip) => Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    )).toList();
  }
}
