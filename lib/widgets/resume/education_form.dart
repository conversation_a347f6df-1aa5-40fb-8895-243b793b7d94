import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:uuid/uuid.dart';
import '../../models/education_model.dart';
import '../../utils/design_tokens.dart';

class EducationForm extends StatefulWidget {
  final List<Education> initialData;
  final Function(List<Education>) onChanged;
  final VoidCallback? onAIAssistRequested;

  const EducationForm({
    super.key,
    this.initialData = const [],
    required this.onChanged,
    this.onAIAssistRequested,
  });

  @override
  State<EducationForm> createState() => _EducationFormState();
}

class _EducationFormState extends State<EducationForm> {
  List<Education> _educations = [];

  @override
  void initState() {
    super.initState();
    _educations = widget.initialData.isNotEmpty 
        ? List.from(widget.initialData)
        : [_createEmptyEducation()];
  }

  Education _createEmptyEducation() {
    return Education(
      id: const Uuid().v4(),
      institution: '',
      degree: '',
      fieldOfStudy: '',
      startDate: DateTime.now(),
      endDate: null,
      isCurrentlyStudying: false,
      gpa: null,
      description: '',
      coursework: [],
      achievements: [],
      location: '',
    );
  }

  void _addEducation() {
    setState(() {
      _educations.add(_createEmptyEducation());
    });
  }

  void _removeEducation(int index) {
    if (_educations.length > 1) {
      setState(() {
        _educations.removeAt(index);
      });
      widget.onChanged(_educations);
    }
  }

  void _updateEducation(int index, Education education) {
    setState(() {
      _educations[index] = education;
    });
    widget.onChanged(_educations);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Education',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add your educational background',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.onAIAssistRequested != null)
                IconButton.outlined(
                  onPressed: widget.onAIAssistRequested,
                  icon: const Icon(Icons.auto_awesome),
                  tooltip: 'Get AI help',
                ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Education entries
          ...List.generate(_educations.length, (index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: _EducationCard(
                key: ValueKey('education_$index'),
                education: _educations[index],
                index: index,
                canRemove: _educations.length > 1,
                onChanged: (education) => _updateEducation(index, education),
                onRemove: () => _removeEducation(index),
              ),
            );
          }),
          
          // Add education button
          Center(
            child: OutlinedButton.icon(
              onPressed: _addEducation,
              icon: const Icon(Icons.add),
              label: const Text('Add Education'),
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _EducationCard extends StatefulWidget {
  final Education education;
  final int index;
  final bool canRemove;
  final Function(Education) onChanged;
  final VoidCallback onRemove;

  const _EducationCard({
    super.key,
    required this.education,
    required this.index,
    required this.canRemove,
    required this.onChanged,
    required this.onRemove,
  });

  @override
  State<_EducationCard> createState() => _EducationCardState();
}

class _EducationCardState extends State<_EducationCard> {
  final _formKey = GlobalKey<FormBuilderState>();
  late Education _education;
  List<String> _coursework = [];
  List<String> _achievements = [];

  @override
  void initState() {
    super.initState();
    _education = widget.education;
    _coursework = List.from(_education.coursework);
    _achievements = List.from(_education.achievements);
    
    // Ensure at least one empty item for dynamic lists
    if (_coursework.isEmpty) _coursework.add('');
    if (_achievements.isEmpty) _achievements.add('');
  }

  void _updateEducation() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      
      // Parse dates
      DateTime startDate;
      DateTime? endDate;
      
      try {
        final startDateStr = formData['startDate'] as String? ?? '';
        final endDateStr = formData['endDate'] as String? ?? '';
        
        if (startDateStr.isNotEmpty) {
          final parts = startDateStr.split('/');
          if (parts.length == 2) {
            final month = int.parse(parts[0]);
            final year = int.parse(parts[1]);
            startDate = DateTime(year, month);
          } else {
            startDate = _education.startDate;
          }
        } else {
          startDate = _education.startDate;
        }
        
        if (endDateStr.isNotEmpty && !(formData['isCurrentlyStudying'] ?? false)) {
          final parts = endDateStr.split('/');
          if (parts.length == 2) {
            final month = int.parse(parts[0]);
            final year = int.parse(parts[1]);
            endDate = DateTime(year, month);
          }
        }
      } catch (e) {
        // Keep existing dates if parsing fails
        startDate = _education.startDate;
        endDate = _education.endDate;
      }

      _education = Education(
        id: _education.id,
        institution: formData['institution'] ?? '',
        degree: formData['degree'] ?? '',
        fieldOfStudy: formData['fieldOfStudy'] ?? '',
        startDate: startDate,
        endDate: endDate,
        isCurrentlyStudying: formData['isCurrentlyStudying'] ?? false,
        gpa: formData['gpa'] != null ? double.tryParse(formData['gpa'].toString()) : null,
        description: formData['description'] ?? '',
        coursework: _coursework.where((c) => c.trim().isNotEmpty).toList(),
        achievements: _achievements.where((a) => a.trim().isNotEmpty).toList(),
        location: formData['location'] ?? '',
      );

      widget.onChanged(_education);
    }
  }

  void _addCoursework() {
    setState(() {
      _coursework.add('');
    });
  }

  void _removeCoursework(int index) {
    if (_coursework.length > 1) {
      setState(() {
        _coursework.removeAt(index);
      });
    }
  }

  void _updateCoursework(int index, String value) {
    setState(() {
      _coursework[index] = value;
    });
    _updateEducation();
  }

  void _addAchievement() {
    setState(() {
      _achievements.add('');
    });
  }

  void _removeAchievement(int index) {
    if (_achievements.length > 1) {
      setState(() {
        _achievements.removeAt(index);
      });
    }
  }

  void _updateAchievement(int index, String value) {
    setState(() {
      _achievements[index] = value;
    });
    _updateEducation();
  }

  String _formatDateForDisplay(DateTime? date) {
    if (date == null) return '';
    return '${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: FormBuilder(
        key: _formKey,
        onChanged: _updateEducation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                  ),
                  child: Icon(
                    Icons.school_outlined,
                    size: 20,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Education ${widget.index + 1}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (widget.canRemove)
                  IconButton(
                    onPressed: widget.onRemove,
                    icon: const Icon(Icons.delete_outline),
                    color: Theme.of(context).colorScheme.error,
                  ),
              ],
            ),

            const SizedBox(height: 20),

            // Institution and Degree
            Row(
              children: [
                Expanded(
                  child: FormBuilderTextField(
                    name: 'institution',
                    initialValue: _education.institution,
                    decoration: _buildInputDecoration('Institution'),
                    validator: FormBuilderValidators.required(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FormBuilderTextField(
                    name: 'degree',
                    initialValue: _education.degree,
                    decoration: _buildInputDecoration('Degree'),
                    validator: FormBuilderValidators.required(),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Field of Study and Location
            Row(
              children: [
                Expanded(
                  child: FormBuilderTextField(
                    name: 'fieldOfStudy',
                    initialValue: _education.fieldOfStudy,
                    decoration: _buildInputDecoration('Field of Study'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FormBuilderTextField(
                    name: 'location',
                    initialValue: _education.location,
                    decoration: _buildInputDecoration('Location'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Dates and GPA
            Row(
              children: [
                Expanded(
                  child: FormBuilderTextField(
                    name: 'startDate',
                    initialValue: _formatDateForDisplay(_education.startDate),
                    decoration: _buildInputDecoration('Start Date (MM/YYYY)'),
                    validator: FormBuilderValidators.required(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FormBuilderTextField(
                    name: 'endDate',
                    initialValue: _formatDateForDisplay(_education.endDate),
                    decoration: _buildInputDecoration('End Date (MM/YYYY)'),
                    enabled: !(_formKey.currentState?.value['isCurrentlyStudying'] ?? _education.isCurrentlyStudying),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FormBuilderTextField(
                    name: 'gpa',
                    initialValue: _education.gpa?.toString() ?? '',
                    decoration: _buildInputDecoration('GPA (Optional)'),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Currently studying checkbox
            FormBuilderCheckbox(
              name: 'isCurrentlyStudying',
              initialValue: _education.isCurrentlyStudying,
              title: const Text('I am currently studying here'),
            ),

            const SizedBox(height: 16),

            // Description
            FormBuilderTextField(
              name: 'description',
              initialValue: _education.description,
              decoration: _buildInputDecoration('Description (Optional)'),
              maxLines: 3,
            ),

            const SizedBox(height: 20),

            // Relevant Coursework
            Text(
              'Relevant Coursework (Optional)',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            ...List.generate(_coursework.length, (index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        initialValue: _coursework[index],
                        decoration: _buildInputDecoration('Course ${index + 1}'),
                        onChanged: (value) => _updateCoursework(index, value),
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (_coursework.length > 1)
                      IconButton(
                        onPressed: () => _removeCoursework(index),
                        icon: const Icon(Icons.remove_circle_outline),
                        color: Theme.of(context).colorScheme.error,
                      ),
                  ],
                ),
              );
            }),

            TextButton.icon(
              onPressed: _addCoursework,
              icon: const Icon(Icons.add),
              label: const Text('Add Course'),
            ),

            const SizedBox(height: 20),

            // Academic Achievements
            Text(
              'Academic Achievements (Optional)',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),

            ...List.generate(_achievements.length, (index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        initialValue: _achievements[index],
                        decoration: _buildInputDecoration('Achievement ${index + 1}'),
                        onChanged: (value) => _updateAchievement(index, value),
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (_achievements.length > 1)
                      IconButton(
                        onPressed: () => _removeAchievement(index),
                        icon: const Icon(Icons.remove_circle_outline),
                        color: Theme.of(context).colorScheme.error,
                      ),
                  ],
                ),
              );
            }),

            TextButton.icon(
              onPressed: _addAchievement,
              icon: const Icon(Icons.add),
              label: const Text('Add Achievement'),
            ),
          ],
        ),
      ),
    );
  }

  InputDecoration _buildInputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.primary,
          width: 2,
        ),
      ),
      filled: true,
      fillColor: Theme.of(context).colorScheme.surface,
    );
  }
}
