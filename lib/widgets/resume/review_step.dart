import 'package:flutter/material.dart';
import '../../models/resume_model.dart';
import '../../models/work_experience_model.dart';
import '../../models/education_model.dart';
import '../../models/skill_model.dart';
import '../../utils/design_tokens.dart';

class ReviewStep extends StatelessWidget {
  final PersonalInfo personalInfo;
  final String summary;
  final List<WorkExperience> workExperience;
  final List<Education> education;
  final List<Skill> skills;
  final ResumeStyle selectedStyle;
  final VoidCallback? onGeneratePressed;
  final bool isGenerating;

  const ReviewStep({
    super.key,
    required this.personalInfo,
    required this.summary,
    required this.workExperience,
    required this.education,
    required this.skills,
    required this.selectedStyle,
    this.onGeneratePressed,
    this.isGenerating = false,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Review Your Resume',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Review all sections before generating your resume',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Style selection
          _buildStyleSection(context),
          
          const SizedBox(height: 24),
          
          // Personal Information
          _buildPersonalInfoSection(context),
          
          const SizedBox(height: 24),
          
          // Summary
          if (summary.isNotEmpty) ...[
            _buildSummarySection(context),
            const SizedBox(height: 24),
          ],
          
          // Work Experience
          if (workExperience.isNotEmpty) ...[
            _buildWorkExperienceSection(context),
            const SizedBox(height: 24),
          ],
          
          // Education
          if (education.isNotEmpty) ...[
            _buildEducationSection(context),
            const SizedBox(height: 24),
          ],
          
          // Skills
          if (skills.isNotEmpty) ...[
            _buildSkillsSection(context),
            const SizedBox(height: 24),
          ],
          
          // Generate button
          _buildGenerateSection(context),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildStyleSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Resume Style',
      icon: Icons.palette_outlined,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getStyleIcon(selectedStyle),
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    selectedStyle.displayName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  Text(
                    selectedStyle.description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Personal Information',
      icon: Icons.person_outline,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoRow(context, 'Name', personalInfo.fullName),
          _buildInfoRow(context, 'Email', personalInfo.email),
          _buildInfoRow(context, 'Phone', personalInfo.phone),
          if (personalInfo.linkedIn?.isNotEmpty ?? false)
            _buildInfoRow(context, 'LinkedIn', personalInfo.linkedIn!),
          if (personalInfo.website?.isNotEmpty ?? false)
            _buildInfoRow(context, 'Website', personalInfo.website!),
          if (personalInfo.github?.isNotEmpty ?? false)
            _buildInfoRow(context, 'GitHub', personalInfo.github!),
        ],
      ),
    );
  }

  Widget _buildSummarySection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Professional Summary',
      icon: Icons.description_outlined,
      child: Text(
        summary,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildWorkExperienceSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Work Experience',
      icon: Icons.work_outline,
      child: Column(
        children: workExperience.map((exp) => _buildWorkExperienceItem(context, exp)).toList(),
      ),
    );
  }

  Widget _buildWorkExperienceItem(BuildContext context, WorkExperience experience) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  experience.jobTitle,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                experience.dateRange,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            experience.company,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          if (experience.location?.isNotEmpty ?? false) ...[
            const SizedBox(height: 2),
            Text(
              experience.location!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
          if (experience.description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              experience.description,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
          if (experience.responsibilities.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...experience.responsibilities.take(3).map((resp) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('• ', style: Theme.of(context).textTheme.bodySmall),
                  Expanded(
                    child: Text(
                      resp,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            )),
            if (experience.responsibilities.length > 3)
              Text(
                '... and ${experience.responsibilities.length - 3} more',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildEducationSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Education',
      icon: Icons.school_outlined,
      child: Column(
        children: education.map((edu) => _buildEducationItem(context, edu)).toList(),
      ),
    );
  }

  Widget _buildEducationItem(BuildContext context, Education education) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  education.fullDegree,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                education.dateRange,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            education.institution,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          if (education.gpa != null) ...[
            const SizedBox(height: 4),
            Text(
              education.gpaDisplay,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSkillsSection(BuildContext context) {
    // Group skills by category
    final skillsByCategory = <SkillCategory, List<Skill>>{};
    for (final skill in skills) {
      skillsByCategory.putIfAbsent(skill.category, () => []).add(skill);
    }

    return _buildSection(
      context,
      title: 'Skills',
      icon: Icons.psychology_outlined,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: skillsByCategory.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  entry.key.displayName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: entry.value.map((skill) => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(DesignTokens.radiusSm),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      '${skill.name} (${skill.level.displayName})',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )).toList(),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGenerateSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.picture_as_pdf,
            size: 48,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Ready to Generate Your Resume?',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your resume will be generated as a PDF file that you can download and share.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: isGenerating ? null : onGeneratePressed,
              icon: isGenerating
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  )
                : const Icon(Icons.download),
              label: Text(isGenerating ? 'Generating...' : 'Generate PDF Resume'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, {
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                ),
                child: Icon(
                  icon,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getStyleIcon(ResumeStyle style) {
    switch (style) {
      case ResumeStyle.professional:
        return Icons.business_center_outlined;
      case ResumeStyle.creative:
        return Icons.palette_outlined;
      case ResumeStyle.minimalist:
        return Icons.minimize_outlined;
      case ResumeStyle.modern:
        return Icons.auto_awesome_outlined;
    }
  }
}
