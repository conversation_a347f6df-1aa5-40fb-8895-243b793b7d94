import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../../models/skill_model.dart';
import '../../utils/design_tokens.dart';

class SkillsForm extends StatefulWidget {
  final List<Skill> initialData;
  final Function(List<Skill>) onChanged;
  final VoidCallback? onAIAssistRequested;

  const SkillsForm({
    super.key,
    this.initialData = const [],
    required this.onChanged,
    this.onAIAssistRequested,
  });

  @override
  State<SkillsForm> createState() => _SkillsFormState();
}

class _SkillsFormState extends State<SkillsForm> {
  List<Skill> _skills = [];
  SkillCategory _selectedCategory = SkillCategory.technical;

  @override
  void initState() {
    super.initState();
    _skills = widget.initialData.isNotEmpty 
        ? List.from(widget.initialData)
        : [];
  }

  void _addSkill(String skillName) {
    if (skillName.trim().isEmpty) return;
    
    final skill = Skill(
      id: const Uuid().v4(),
      name: skillName.trim(),
      category: _selectedCategory,
      level: SkillLevel.intermediate,
    );
    
    setState(() {
      _skills.add(skill);
    });
    widget.onChanged(_skills);
  }

  void _removeSkill(int index) {
    setState(() {
      _skills.removeAt(index);
    });
    widget.onChanged(_skills);
  }

  void _updateSkill(int index, Skill skill) {
    setState(() {
      _skills[index] = skill;
    });
    widget.onChanged(_skills);
  }

  List<Skill> _getSkillsByCategory(SkillCategory category) {
    return _skills.where((skill) => skill.category == category).toList();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Skills',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add your technical and soft skills',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.onAIAssistRequested != null)
                IconButton.outlined(
                  onPressed: widget.onAIAssistRequested,
                  icon: const Icon(Icons.auto_awesome),
                  tooltip: 'Get AI help',
                ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Category tabs
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: SkillCategory.values.length,
              itemBuilder: (context, index) {
                final category = SkillCategory.values[index];
                final isSelected = category == _selectedCategory;
                final skillCount = _getSkillsByCategory(category).length;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text('${category.displayName} ($skillCount)'),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Quick add from common skills
          if (_selectedCategory.commonSkills.isNotEmpty) ...[
            Text(
              'Common ${_selectedCategory.displayName}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _selectedCategory.commonSkills.map((skillName) {
                final alreadyAdded = _skills.any((skill) => 
                  skill.name.toLowerCase() == skillName.toLowerCase());
                
                return ActionChip(
                  label: Text(skillName),
                  onPressed: alreadyAdded ? null : () => _addSkill(skillName),
                  backgroundColor: alreadyAdded 
                    ? Theme.of(context).colorScheme.surfaceContainerHighest
                    : null,
                );
              }).toList(),
            ),
            const SizedBox(height: 24),
          ],
          
          // Custom skill input
          _SkillInputCard(
            category: _selectedCategory,
            onSkillAdded: _addSkill,
          ),
          
          const SizedBox(height: 24),
          
          // Skills list for current category
          _SkillsList(
            skills: _getSkillsByCategory(_selectedCategory),
            onSkillUpdated: (index, skill) {
              final globalIndex = _skills.indexOf(_getSkillsByCategory(_selectedCategory)[index]);
              if (globalIndex != -1) {
                _updateSkill(globalIndex, skill);
              }
            },
            onSkillRemoved: (index) {
              final skillToRemove = _getSkillsByCategory(_selectedCategory)[index];
              final globalIndex = _skills.indexOf(skillToRemove);
              if (globalIndex != -1) {
                _removeSkill(globalIndex);
              }
            },
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _SkillInputCard extends StatefulWidget {
  final SkillCategory category;
  final Function(String) onSkillAdded;

  const _SkillInputCard({
    required this.category,
    required this.onSkillAdded,
  });

  @override
  State<_SkillInputCard> createState() => _SkillInputCardState();
}

class _SkillInputCardState extends State<_SkillInputCard> {
  final _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _addSkill() {
    if (_controller.text.trim().isNotEmpty) {
      widget.onSkillAdded(_controller.text.trim());
      _controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                ),
                child: Icon(
                  Icons.add_circle_outline,
                  size: 20,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Add ${widget.category.displayName}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _controller,
                  decoration: InputDecoration(
                    labelText: 'Skill name',
                    hintText: 'e.g., JavaScript, Leadership, etc.',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                  onFieldSubmitted: (_) => _addSkill(),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _addSkill,
                child: const Text('Add'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _SkillsList extends StatelessWidget {
  final List<Skill> skills;
  final Function(int, Skill) onSkillUpdated;
  final Function(int) onSkillRemoved;

  const _SkillsList({
    required this.skills,
    required this.onSkillUpdated,
    required this.onSkillRemoved,
  });

  @override
  Widget build(BuildContext context) {
    if (skills.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.psychology_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 16),
              Text(
                'No skills added yet',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add skills using the form above or select from common skills',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: List.generate(skills.length, (index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _SkillCard(
            skill: skills[index],
            onUpdated: (skill) => onSkillUpdated(index, skill),
            onRemoved: () => onSkillRemoved(index),
          ),
        );
      }),
    );
  }
}

class _SkillCard extends StatefulWidget {
  final Skill skill;
  final Function(Skill) onUpdated;
  final VoidCallback onRemoved;

  const _SkillCard({
    required this.skill,
    required this.onUpdated,
    required this.onRemoved,
  });

  @override
  State<_SkillCard> createState() => _SkillCardState();
}

class _SkillCardState extends State<_SkillCard> {
  late SkillLevel _selectedLevel;
  int? _yearsOfExperience;

  @override
  void initState() {
    super.initState();
    _selectedLevel = widget.skill.level;
    _yearsOfExperience = widget.skill.yearsOfExperience;
  }

  void _updateSkill() {
    final updatedSkill = widget.skill.copyWith(
      level: _selectedLevel,
      yearsOfExperience: _yearsOfExperience,
    );
    widget.onUpdated(updatedSkill);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.skill.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                onPressed: widget.onRemoved,
                icon: const Icon(Icons.delete_outline),
                color: Theme.of(context).colorScheme.error,
                iconSize: 20,
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Skill level
          Text(
            'Proficiency Level',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),

          Wrap(
            spacing: 8,
            children: SkillLevel.values.map((level) {
              final isSelected = level == _selectedLevel;
              return FilterChip(
                label: Text(level.displayName),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _selectedLevel = level;
                  });
                  _updateSkill();
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 12),

          // Years of experience (optional)
          Row(
            children: [
              Expanded(
                child: Text(
                  'Years of Experience (Optional)',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
              SizedBox(
                width: 100,
                child: TextFormField(
                  initialValue: _yearsOfExperience?.toString() ?? '',
                  decoration: InputDecoration(
                    hintText: 'Years',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusSm),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() {
                      _yearsOfExperience = int.tryParse(value);
                    });
                    _updateSkill();
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Skill level indicator
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: _selectedLevel.percentage / 100,
                  backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${_selectedLevel.percentage}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
