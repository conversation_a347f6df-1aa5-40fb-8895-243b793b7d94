import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../models/work_experience_model.dart';
import '../../utils/design_tokens.dart';

class WorkExperienceForm extends StatefulWidget {
  final List<WorkExperience> initialData;
  final Function(List<WorkExperience>) onChanged;
  final VoidCallback? onAIAssistRequested;

  const WorkExperienceForm({
    super.key,
    this.initialData = const [],
    required this.onChanged,
    this.onAIAssistRequested,
  });

  @override
  State<WorkExperienceForm> createState() => _WorkExperienceFormState();
}

class _WorkExperienceFormState extends State<WorkExperienceForm> {
  List<WorkExperience> _experiences = [];

  @override
  void initState() {
    super.initState();
    _experiences = widget.initialData.isNotEmpty 
        ? List.from(widget.initialData)
        : [_createEmptyExperience()];
  }

  WorkExperience _createEmptyExperience() {
    return WorkExperience(
      id: '',
      jobTitle: '',
      company: '',
      location: '',
      startDate: DateTime.now(),
      endDate: null,
      isCurrentJob: false,
      description: '',
      responsibilities: [],
      achievements: [],
    );
  }

  void _addExperience() {
    setState(() {
      _experiences.add(_createEmptyExperience());
    });
  }

  void _removeExperience(int index) {
    if (_experiences.length > 1) {
      setState(() {
        _experiences.removeAt(index);
      });
      widget.onChanged(_experiences);
    }
  }

  void _updateExperience(int index, WorkExperience experience) {
    setState(() {
      _experiences[index] = experience;
    });
    widget.onChanged(_experiences);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Work Experience',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add your professional experience, starting with the most recent',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.onAIAssistRequested != null)
                IconButton.outlined(
                  onPressed: widget.onAIAssistRequested,
                  icon: const Icon(Icons.auto_awesome),
                  tooltip: 'Get AI help',
                ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // Experience entries
          ...List.generate(_experiences.length, (index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: _WorkExperienceCard(
                key: ValueKey('experience_$index'),
                experience: _experiences[index],
                index: index,
                canRemove: _experiences.length > 1,
                onChanged: (experience) => _updateExperience(index, experience),
                onRemove: () => _removeExperience(index),
              ),
            );
          }),
          
          // Add experience button
          Center(
            child: OutlinedButton.icon(
              onPressed: _addExperience,
              icon: const Icon(Icons.add),
              label: const Text('Add Another Experience'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _WorkExperienceCard extends StatefulWidget {
  final WorkExperience experience;
  final int index;
  final bool canRemove;
  final Function(WorkExperience) onChanged;
  final VoidCallback onRemove;

  const _WorkExperienceCard({
    super.key,
    required this.experience,
    required this.index,
    required this.canRemove,
    required this.onChanged,
    required this.onRemove,
  });

  @override
  State<_WorkExperienceCard> createState() => _WorkExperienceCardState();
}

class _WorkExperienceCardState extends State<_WorkExperienceCard> {
  final _formKey = GlobalKey<FormBuilderState>();
  late WorkExperience _experience;
  List<String> _responsibilities = [];

  @override
  void initState() {
    super.initState();
    _experience = widget.experience;
    _responsibilities = List.from(_experience.responsibilities);
    if (_responsibilities.isEmpty) {
      _responsibilities.add('');
    }
  }

  void _updateExperience() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;

      // Parse dates from string format
      DateTime? startDate;
      DateTime? endDate;

      try {
        final startDateStr = formData['startDate'] as String?;
        if (startDateStr != null && startDateStr.isNotEmpty) {
          // Try to parse MM/YYYY format
          final parts = startDateStr.split('/');
          if (parts.length == 2) {
            final month = int.tryParse(parts[0]);
            final year = int.tryParse(parts[1]);
            if (month != null && year != null) {
              startDate = DateTime(year, month);
            }
          }
        }
        startDate ??= _experience.startDate;

        if (formData['isCurrentJob'] != true) {
          final endDateStr = formData['endDate'] as String?;
          if (endDateStr != null && endDateStr.isNotEmpty) {
            final parts = endDateStr.split('/');
            if (parts.length == 2) {
              final month = int.tryParse(parts[0]);
              final year = int.tryParse(parts[1]);
              if (month != null && year != null) {
                endDate = DateTime(year, month);
              }
            }
          }
        }
      } catch (e) {
        // Keep existing dates if parsing fails
        startDate = _experience.startDate;
        endDate = _experience.endDate;
      }

      _experience = WorkExperience(
        id: _experience.id,
        jobTitle: formData['jobTitle'] ?? '',
        company: formData['company'] ?? '',
        location: formData['location'] ?? '',
        startDate: startDate,
        endDate: endDate,
        isCurrentJob: formData['isCurrentJob'] ?? false,
        description: formData['description'] ?? '',
        responsibilities: _responsibilities.where((r) => r.trim().isNotEmpty).toList(),
        achievements: [], // TODO: Add achievements form
      );

      widget.onChanged(_experience);
    }
  }

  void _addResponsibility() {
    setState(() {
      _responsibilities.add('');
    });
  }

  void _removeResponsibility(int index) {
    if (_responsibilities.length > 1) {
      setState(() {
        _responsibilities.removeAt(index);
      });
      _updateExperience();
    }
  }

  void _updateResponsibility(int index, String value) {
    setState(() {
      _responsibilities[index] = value;
    });
    _updateExperience();
  }

  String _formatDateForDisplay(DateTime? date) {
    if (date == null) return '';
    return '${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: FormBuilder(
        key: _formKey,
        onChanged: _updateExperience,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                  ),
                  child: Icon(
                    Icons.work_outline,
                    size: 20,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Experience ${widget.index + 1}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (widget.canRemove)
                  IconButton(
                    onPressed: widget.onRemove,
                    icon: const Icon(Icons.delete_outline),
                    color: Theme.of(context).colorScheme.error,
                  ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Job title and company
            Row(
              children: [
                Expanded(
                  child: FormBuilderTextField(
                    name: 'jobTitle',
                    initialValue: _experience.jobTitle,
                    decoration: _buildInputDecoration('Job Title'),
                    validator: FormBuilderValidators.required(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FormBuilderTextField(
                    name: 'company',
                    initialValue: _experience.company,
                    decoration: _buildInputDecoration('Company'),
                    validator: FormBuilderValidators.required(),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Location
            FormBuilderTextField(
              name: 'location',
              initialValue: _experience.location,
              decoration: _buildInputDecoration('Location'),
            ),
            
            const SizedBox(height: 16),
            
            // Dates
            Row(
              children: [
                Expanded(
                  child: FormBuilderTextField(
                    name: 'startDate',
                    initialValue: _formatDateForDisplay(_experience.startDate),
                    decoration: _buildInputDecoration('Start Date (MM/YYYY)'),
                    validator: FormBuilderValidators.required(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FormBuilderTextField(
                    name: 'endDate',
                    initialValue: _formatDateForDisplay(_experience.endDate),
                    decoration: _buildInputDecoration('End Date (MM/YYYY)'),
                    enabled: !(_formKey.currentState?.value['isCurrentJob'] ?? _experience.isCurrentJob),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Current job checkbox
            FormBuilderCheckbox(
              name: 'isCurrentJob',
              initialValue: _experience.isCurrentJob,
              title: const Text('I currently work here'),
            ),
            
            const SizedBox(height: 16),
            
            // Description
            FormBuilderTextField(
              name: 'description',
              initialValue: _experience.description,
              decoration: _buildInputDecoration('Job Description'),
              maxLines: 3,
            ),
            
            const SizedBox(height: 20),
            
            // Responsibilities
            Text(
              'Key Responsibilities',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            
            ...List.generate(_responsibilities.length, (index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        initialValue: _responsibilities[index],
                        decoration: _buildInputDecoration('Responsibility ${index + 1}'),
                        onChanged: (value) => _updateResponsibility(index, value),
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (_responsibilities.length > 1)
                      IconButton(
                        onPressed: () => _removeResponsibility(index),
                        icon: const Icon(Icons.remove_circle_outline),
                        color: Theme.of(context).colorScheme.error,
                      ),
                  ],
                ),
              );
            }),
            
            TextButton.icon(
              onPressed: _addResponsibility,
              icon: const Icon(Icons.add),
              label: const Text('Add Responsibility'),
            ),
          ],
        ),
      ),
    );
  }

  InputDecoration _buildInputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.primary,
          width: 2,
        ),
      ),
      filled: true,
      fillColor: Theme.of(context).colorScheme.surface,
    );
  }
}
