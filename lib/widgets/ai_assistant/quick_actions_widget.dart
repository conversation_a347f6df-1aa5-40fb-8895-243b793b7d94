import 'package:flutter/material.dart';
import '../../models/ai_chat_model.dart';
import '../../utils/design_tokens.dart';

class QuickActionsWidget extends StatelessWidget {
  final AIAssistantContext context;
  final Function(String) onActionTapped;

  const QuickActionsWidget({
    super.key,
    required this.context,
    required this.onActionTapped,
  });

  @override
  Widget build(BuildContext context) {
    final actions = _getQuickActions(this.context);
    
    if (actions.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: actions.map((action) {
            return _buildActionChip(context, action);
          }).toList(),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildActionChip(BuildContext context, QuickAction action) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onActionTapped(action.prompt),
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                action.icon,
                size: 18,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  action.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<QuickAction> _getQuickActions(AIAssistantContext context) {
    switch (context) {
      case AIAssistantContext.general:
        return [
          QuickAction(
            title: 'Resume tips',
            icon: Icons.lightbulb_outline,
            prompt: 'Give me 5 essential tips for creating a great resume',
          ),
          QuickAction(
            title: 'Common mistakes',
            icon: Icons.warning_outlined,
            prompt: 'What are the most common resume mistakes to avoid?',
          ),
          QuickAction(
            title: 'Industry trends',
            icon: Icons.trending_up,
            prompt: 'What are the current resume trends in 2024?',
          ),
          QuickAction(
            title: 'ATS basics',
            icon: Icons.computer,
            prompt: 'Explain how ATS systems work and how to optimize for them',
          ),
        ];

      case AIAssistantContext.resumeCreation:
        return [
          QuickAction(
            title: 'Getting started',
            icon: Icons.play_arrow,
            prompt: 'How do I start creating my resume? What information do I need?',
          ),
          QuickAction(
            title: 'Choose format',
            icon: Icons.format_list_bulleted,
            prompt: 'What resume format should I use for my industry?',
          ),
          QuickAction(
            title: 'Section order',
            icon: Icons.reorder,
            prompt: 'In what order should I arrange the sections of my resume?',
          ),
          QuickAction(
            title: 'Length guidelines',
            icon: Icons.straighten,
            prompt: 'How long should my resume be?',
          ),
        ];

      case AIAssistantContext.jobDescriptionHelp:
        return [
          QuickAction(
            title: 'Action verbs',
            icon: Icons.flash_on,
            prompt: 'Give me a list of powerful action verbs for job descriptions',
          ),
          QuickAction(
            title: 'Quantify achievements',
            icon: Icons.analytics,
            prompt: 'How can I quantify my achievements in job descriptions?',
          ),
          QuickAction(
            title: 'Improve bullets',
            icon: Icons.edit,
            prompt: 'Help me improve these job description bullets: [paste your bullets here]',
          ),
          QuickAction(
            title: 'STAR method',
            icon: Icons.star,
            prompt: 'Explain the STAR method for writing job descriptions',
          ),
        ];

      case AIAssistantContext.skillSuggestions:
        return [
          QuickAction(
            title: 'Technical skills',
            icon: Icons.computer,
            prompt: 'What technical skills are in demand for my field?',
          ),
          QuickAction(
            title: 'Soft skills',
            icon: Icons.people,
            prompt: 'What soft skills should I highlight on my resume?',
          ),
          QuickAction(
            title: 'Certifications',
            icon: Icons.verified,
            prompt: 'What certifications would boost my resume in my industry?',
          ),
          QuickAction(
            title: 'Skill levels',
            icon: Icons.bar_chart,
            prompt: 'How should I indicate my skill levels on my resume?',
          ),
        ];

      case AIAssistantContext.summaryGeneration:
        return [
          QuickAction(
            title: 'Write summary',
            icon: Icons.create,
            prompt: 'Help me write a professional summary for my resume',
          ),
          QuickAction(
            title: 'Summary examples',
            icon: Icons.library_books,
            prompt: 'Show me examples of great professional summaries',
          ),
          QuickAction(
            title: 'Key elements',
            icon: Icons.checklist,
            prompt: 'What elements should I include in my professional summary?',
          ),
          QuickAction(
            title: 'Length guide',
            icon: Icons.short_text,
            prompt: 'How long should my professional summary be?',
          ),
        ];

      case AIAssistantContext.atsOptimization:
        return [
          QuickAction(
            title: 'ATS keywords',
            icon: Icons.search,
            prompt: 'How do I find and use the right keywords for ATS?',
          ),
          QuickAction(
            title: 'Format tips',
            icon: Icons.format_align_left,
            prompt: 'What formatting should I avoid for ATS compatibility?',
          ),
          QuickAction(
            title: 'Test my resume',
            icon: Icons.quiz,
            prompt: 'How can I test if my resume is ATS-friendly?',
          ),
          QuickAction(
            title: 'Common issues',
            icon: Icons.error_outline,
            prompt: 'What are common ATS issues that cause resumes to be rejected?',
          ),
        ];
    }
  }
}

class QuickAction {
  final String title;
  final IconData icon;
  final String prompt;

  const QuickAction({
    required this.title,
    required this.icon,
    required this.prompt,
  });
}
