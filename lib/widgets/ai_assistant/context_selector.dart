import 'package:flutter/material.dart';
import '../../models/ai_chat_model.dart';
import '../../utils/design_tokens.dart';

class ContextSelector extends StatelessWidget {
  final AIAssistantContext currentContext;
  final Function(AIAssistantContext) onContextSelected;

  const ContextSelector({
    super.key,
    required this.currentContext,
    required this.onContextSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusXl),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Icon(
                  Icons.tune,
                  size: 32,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 12),
                Text(
                  'Choose Assistant Mode',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select the type of help you need to get more targeted assistance',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          
          // Context options
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: AIAssistantContext.values.map((contextOption) {
                return _buildContextOption(context, contextOption);
              }).toList(),
            ),
          ),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildContextOption(BuildContext context, AIAssistantContext contextOption) {
    final isSelected = contextOption == currentContext;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onContextSelected(contextOption),
          borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected 
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
              border: isSelected 
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    )
                  : null,
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.surfaceContainerHigh,
                    borderRadius: BorderRadius.circular(DesignTokens.radiusMd),
                  ),
                  child: Icon(
                    _getContextIcon(contextOption),
                    size: 20,
                    color: isSelected 
                        ? Theme.of(context).colorScheme.onPrimary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        contextOption.displayName,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected 
                              ? Theme.of(context).colorScheme.onPrimaryContainer
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getContextDescription(contextOption),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isSelected 
                              ? Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8)
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getContextIcon(AIAssistantContext context) {
    switch (context) {
      case AIAssistantContext.general:
        return Icons.help_outline;
      case AIAssistantContext.resumeCreation:
        return Icons.description_outlined;
      case AIAssistantContext.jobDescriptionHelp:
        return Icons.work_outline;
      case AIAssistantContext.skillSuggestions:
        return Icons.psychology_outlined;
      case AIAssistantContext.summaryGeneration:
        return Icons.summarize_outlined;
      case AIAssistantContext.atsOptimization:
        return Icons.tune_outlined;
    }
  }

  String _getContextDescription(AIAssistantContext context) {
    switch (context) {
      case AIAssistantContext.general:
        return 'General resume and career advice';
      case AIAssistantContext.resumeCreation:
        return 'Step-by-step resume building guidance';
      case AIAssistantContext.jobDescriptionHelp:
        return 'Improve job descriptions and bullet points';
      case AIAssistantContext.skillSuggestions:
        return 'Get relevant skill recommendations';
      case AIAssistantContext.summaryGeneration:
        return 'Create compelling professional summaries';
      case AIAssistantContext.atsOptimization:
        return 'Optimize for applicant tracking systems';
    }
  }
}
