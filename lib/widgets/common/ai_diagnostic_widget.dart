import 'package:flutter/material.dart';
import '../../services/ai_assistant_service.dart';
import '../../models/ai_chat_model.dart';
import '../../utils/design_tokens.dart';

class AIDiagnosticWidget extends StatefulWidget {
  const AIDiagnosticWidget({super.key});

  @override
  State<AIDiagnosticWidget> createState() => _AIDiagnosticWidgetState();
}

class _AIDiagnosticWidgetState extends State<AIDiagnosticWidget> {
  late final AIAssistantService _aiService;
  bool _isTestingConnection = false;
  bool _isTestingMessage = false;
  String? _connectionResult;
  String? _messageResult;
  String? _errorMessage;
  final TextEditingController _testMessageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _aiService = AIAssistantService();
    _testMessageController.text = 'Hello, can you help me with my resume?';
  }

  @override
  void dispose() {
    _testMessageController.dispose();
    super.dispose();
  }

  Future<void> _testConnection() async {
    setState(() {
      _isTestingConnection = true;
      _connectionResult = null;
      _errorMessage = null;
    });

    try {
      final success = await _aiService.testConnection();
      setState(() {
        _connectionResult = success ? 'Connection successful!' : 'Connection failed';
        _isTestingConnection = false;
      });
    } catch (e) {
      setState(() {
        _connectionResult = 'Connection failed';
        _errorMessage = e.toString();
        _isTestingConnection = false;
      });
    }
  }

  Future<void> _testMessage() async {
    if (_testMessageController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a test message';
      });
      return;
    }

    setState(() {
      _isTestingMessage = true;
      _messageResult = null;
      _errorMessage = null;
    });

    try {
      final response = await _aiService.sendMessage(
        message: _testMessageController.text.trim(),
        context: AIAssistantContext.general,
        timeout: const Duration(seconds: 15),
      );
      
      setState(() {
        _messageResult = response;
        _isTestingMessage = false;
      });
    } catch (e) {
      setState(() {
        _messageResult = null;
        _errorMessage = e.toString();
        _isTestingMessage = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Assistant Diagnostics',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: DesignTokens.primaryBlue,
              ),
            ),
            const SizedBox(height: 16),
            
            // Service Status
            _buildStatusSection(),
            const SizedBox(height: 16),
            
            // Connection Test
            _buildConnectionTestSection(),
            const SizedBox(height: 16),
            
            // Message Test
            _buildMessageTestSection(),
            
            // Error Display
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              _buildErrorSection(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Status',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(
              _aiService.isInitialized ? Icons.check_circle : Icons.error,
              color: _aiService.isInitialized ? Colors.green : Colors.red,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              _aiService.isInitialized ? 'Initialized' : 'Not Initialized',
              style: TextStyle(
                color: _aiService.isInitialized ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        if (!_aiService.isInitialized && _aiService.initializationError != null) ...[
          const SizedBox(height: 4),
          Text(
            'Error: ${_aiService.initializationError}',
            style: const TextStyle(color: Colors.red, fontSize: 12),
          ),
        ],
      ],
    );
  }

  Widget _buildConnectionTestSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Connection Test',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: _isTestingConnection ? null : _testConnection,
              child: _isTestingConnection
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Test Connection'),
            ),
            if (_connectionResult != null) ...[
              const SizedBox(width: 16),
              Icon(
                _connectionResult!.contains('successful') ? Icons.check_circle : Icons.error,
                color: _connectionResult!.contains('successful') ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                _connectionResult!,
                style: TextStyle(
                  color: _connectionResult!.contains('successful') ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildMessageTestSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Message Test',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _testMessageController,
          decoration: const InputDecoration(
            hintText: 'Enter a test message...',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: _isTestingMessage ? null : _testMessage,
          child: _isTestingMessage
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Send Test Message'),
        ),
        if (_messageResult != null) ...[
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              border: Border.all(color: Colors.green.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'AI Response:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.green.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _messageResult!,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildErrorSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.error, color: Colors.red.shade700, size: 20),
              const SizedBox(width: 8),
              Text(
                'Error Details:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.red.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage!,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }
}
