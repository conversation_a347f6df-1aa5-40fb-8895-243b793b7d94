import 'package:flutter/foundation.dart';
import 'package:firebase_ai/firebase_ai.dart';
import '../services/ai_assistant_service.dart';
import '../models/ai_chat_model.dart';

class AIDebug {
  static Future<void> testAIConfiguration() async {
    if (kDebugMode) {
      print('🔍 Testing AI Configuration...');
      
      try {
        // Test 1: Check if Firebase AI is available
        print('📋 Test 1: Checking Firebase AI availability...');
        final googleAI = FirebaseAI.googleAI();
        print('✅ Firebase AI instance created successfully');
        
        // Test 2: Try to create a model
        print('📋 Test 2: Creating Gemini model...');
        final model = googleAI.generativeModel(model: 'gemini-1.5-flash');
        print('✅ Gemini model created successfully');
        
        // Test 3: Try a simple API call
        print('📋 Test 3: Testing simple API call...');
        final content = [Content.text('Hello, can you respond with "AI is working"?')];
        final response = await model.generateContent(
          content,
          generationConfig: GenerationConfig(
            temperature: 0.7,
            maxOutputTokens: 50,
          ),
        );
        
        if (response.text != null && response.text!.isNotEmpty) {
          print('✅ Simple API call successful');
          print('📝 Response: ${response.text}');
        } else {
          print('❌ Simple API call returned empty response');
        }
        
        // Test 4: Test AI Assistant Service
        print('📋 Test 4: Testing AI Assistant Service...');
        final aiService = AIAssistantService();
        final testResponse = await aiService.sendMessage(
          message: 'Hello, please respond with "AI Assistant is working"',
          context: AIAssistantContext.general,
        );
        
        print('✅ AI Assistant Service test successful');
        print('📝 Response: $testResponse');
        
      } catch (e) {
        print('❌ AI Configuration test failed: $e');
        _printAISetupInstructions();
      }
    }
  }
  
  static void _printAISetupInstructions() {
    if (kDebugMode) {
      print('');
      print('🛠️ AI Setup Instructions:');
      print('1. Go to Google Cloud Console: https://console.cloud.google.com/');
      print('2. Select your project: resume-d24cb');
      print('3. Enable the Generative AI API:');
      print('   https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com');
      print('4. Create an API key:');
      print('   https://console.cloud.google.com/apis/credentials');
      print('5. In Firebase Console, go to Project Settings > General');
      print('6. Add the API key to your Firebase configuration');
      print('7. Make sure Firebase AI is enabled in your project');
      print('');
      print('Alternative: Check if you need to use a different model name');
      print('Available models: gemini-1.5-flash, gemini-1.5-pro, gemini-1.0-pro');
      print('');
    }
  }
  
  static void logAIOperation(String operation, {String? details}) {
    if (kDebugMode) {
      final detailsStr = details != null ? ' - $details' : '';
      print('🤖 AI $operation$detailsStr');
    }
  }
  
  static void logAIError(String operation, String error) {
    if (kDebugMode) {
      print('❌ AI $operation failed: $error');
    }
  }
  
  static void logAISuccess(String operation, {String? details}) {
    if (kDebugMode) {
      final detailsStr = details != null ? ' - $details' : '';
      print('✅ AI $operation successful$detailsStr');
    }
  }
}
