import 'package:equatable/equatable.dart';

enum MessageType {
  user,
  assistant,
  system,
}

enum MessageStatus {
  sending,
  sent,
  error,
}

class ChatMessage extends Equatable {
  final String id;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final MessageStatus status;
  final String? error;
  final Map<String, dynamic>? metadata;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    this.status = MessageStatus.sent,
    this.error,
    this.metadata,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.user,
      ),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      error: json['error'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'error': error,
      'metadata': metadata,
    };
  }

  ChatMessage copyWith({
    String? id,
    String? content,
    MessageType? type,
    DateTime? timestamp,
    MessageStatus? status,
    String? error,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      error: error ?? this.error,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        content,
        type,
        timestamp,
        status,
        error,
        metadata,
      ];
}

class ChatConversation extends Equatable {
  final String id;
  final String title;
  final List<ChatMessage> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? userId;
  final Map<String, dynamic>? context;

  const ChatConversation({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
    this.userId,
    this.context,
  });

  factory ChatConversation.fromJson(Map<String, dynamic> json) {
    return ChatConversation(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      messages: (json['messages'] as List?)
          ?.map((e) => ChatMessage.fromJson(e))
          .toList() ?? [],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      userId: json['userId'],
      context: json['context'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((e) => e.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'userId': userId,
      'context': context,
    };
  }

  ChatConversation copyWith({
    String? id,
    String? title,
    List<ChatMessage>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    Map<String, dynamic>? context,
  }) {
    return ChatConversation(
      id: id ?? this.id,
      title: title ?? this.title,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      context: context ?? this.context,
    );
  }

  ChatConversation addMessage(ChatMessage message) {
    return copyWith(
      messages: [...messages, message],
      updatedAt: DateTime.now(),
    );
  }

  ChatConversation updateMessage(String messageId, ChatMessage updatedMessage) {
    final updatedMessages = messages.map((msg) {
      return msg.id == messageId ? updatedMessage : msg;
    }).toList();

    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        messages,
        createdAt,
        updatedAt,
        userId,
        context,
      ];
}

enum AIAssistantContext {
  general,
  resumeCreation,
  jobDescriptionHelp,
  skillSuggestions,
  summaryGeneration,
  atsOptimization,
}

extension AIAssistantContextExtension on AIAssistantContext {
  String get displayName {
    switch (this) {
      case AIAssistantContext.general:
        return 'General Help';
      case AIAssistantContext.resumeCreation:
        return 'Resume Creation';
      case AIAssistantContext.jobDescriptionHelp:
        return 'Job Description Help';
      case AIAssistantContext.skillSuggestions:
        return 'Skill Suggestions';
      case AIAssistantContext.summaryGeneration:
        return 'Summary Generation';
      case AIAssistantContext.atsOptimization:
        return 'ATS Optimization';
    }
  }

  String get systemPrompt {
    switch (this) {
      case AIAssistantContext.general:
        return 'You are a helpful AI assistant specializing in resume building and career advice. Provide clear, actionable advice to help users create better resumes and advance their careers.';
      case AIAssistantContext.resumeCreation:
        return 'You are an expert resume writer. Help users create compelling, ATS-friendly resumes by providing specific suggestions for content, formatting, and optimization.';
      case AIAssistantContext.jobDescriptionHelp:
        return 'You are an expert at writing impactful job descriptions for resumes. Help users transform their work experience into compelling bullet points with strong action verbs and quantifiable achievements.';
      case AIAssistantContext.skillSuggestions:
        return 'You are a career advisor specializing in skill development. Suggest relevant technical and soft skills based on the user\'s industry, role, and career goals.';
      case AIAssistantContext.summaryGeneration:
        return 'You are an expert at writing professional summaries. Create compelling, concise summaries that highlight the user\'s key strengths, experience, and value proposition.';
      case AIAssistantContext.atsOptimization:
        return 'You are an ATS (Applicant Tracking System) optimization expert. Help users optimize their resumes to pass through ATS filters while maintaining readability and impact.';
    }
  }
}
