part of 'ai_assistant_bloc.dart';

abstract class AIAssistantState extends Equatable {
  const AIAssistantState();

  @override
  List<Object?> get props => [];
}

class AIAssistantInitial extends AIAssistantState {}

class AIAssistantLoading extends AIAssistantState {}

class AIAssistantLoaded extends AIAssistantState {
  final ChatConversation? currentConversation;
  final List<ChatConversation> conversations;
  final AIAssistantContext currentContext;
  final Map<String, dynamic>? additionalContext;

  const AIAssistantLoaded({
    this.currentConversation,
    this.conversations = const [],
    this.currentContext = AIAssistantContext.general,
    this.additionalContext,
  });

  @override
  List<Object?> get props => [
        currentConversation,
        conversations,
        currentContext,
        additionalContext,
      ];

  AIAssistantLoaded copyWith({
    ChatConversation? currentConversation,
    List<ChatConversation>? conversations,
    AIAssistantContext? currentContext,
    Map<String, dynamic>? additionalContext,
  }) {
    return AIAssistantLoaded(
      currentConversation: currentConversation ?? this.currentConversation,
      conversations: conversations ?? this.conversations,
      currentContext: currentContext ?? this.currentContext,
      additionalContext: additionalContext ?? this.additionalContext,
    );
  }
}

class AIAssistantMessageSending extends AIAssistantState {
  final ChatConversation conversation;
  final ChatMessage pendingMessage;

  const AIAssistantMessageSending({
    required this.conversation,
    required this.pendingMessage,
  });

  @override
  List<Object?> get props => [conversation, pendingMessage];
}

class AIAssistantMessageReceived extends AIAssistantState {
  final ChatConversation conversation;
  final ChatMessage userMessage;
  final ChatMessage assistantMessage;

  const AIAssistantMessageReceived({
    required this.conversation,
    required this.userMessage,
    required this.assistantMessage,
  });

  @override
  List<Object?> get props => [conversation, userMessage, assistantMessage];
}

class AIAssistantError extends AIAssistantState {
  final String message;
  final ChatConversation? conversation;
  final String? failedMessageId;

  const AIAssistantError({
    required this.message,
    this.conversation,
    this.failedMessageId,
  });

  @override
  List<Object?> get props => [message, conversation, failedMessageId];
}

class AIAssistantJobDescriptionGenerated extends AIAssistantState {
  final List<String> bullets;
  final String jobTitle;
  final String company;

  const AIAssistantJobDescriptionGenerated({
    required this.bullets,
    required this.jobTitle,
    required this.company,
  });

  @override
  List<Object?> get props => [bullets, jobTitle, company];
}

class AIAssistantSkillSuggestionsGenerated extends AIAssistantState {
  final List<String> skills;
  final String jobTitle;
  final String industry;

  const AIAssistantSkillSuggestionsGenerated({
    required this.skills,
    required this.jobTitle,
    required this.industry,
  });

  @override
  List<Object?> get props => [skills, jobTitle, industry];
}

class AIAssistantSummaryGenerated extends AIAssistantState {
  final String summary;
  final String? targetJobTitle;

  const AIAssistantSummaryGenerated({
    required this.summary,
    this.targetJobTitle,
  });

  @override
  List<Object?> get props => [summary, targetJobTitle];
}

class AIAssistantATSOptimizationGenerated extends AIAssistantState {
  final String optimizedContent;
  final String originalContent;

  const AIAssistantATSOptimizationGenerated({
    required this.optimizedContent,
    required this.originalContent,
  });

  @override
  List<Object?> get props => [optimizedContent, originalContent];
}

class AIAssistantImprovementSuggestionsGenerated extends AIAssistantState {
  final List<String> suggestions;
  final ResumeModel resume;

  const AIAssistantImprovementSuggestionsGenerated({
    required this.suggestions,
    required this.resume,
  });

  @override
  List<Object?> get props => [suggestions, resume];
}

class AIAssistantNewConversationCreated extends AIAssistantState {
  final ChatConversation conversation;

  const AIAssistantNewConversationCreated({required this.conversation});

  @override
  List<Object?> get props => [conversation];
}

class AIAssistantConversationRemoved extends AIAssistantState {
  final String conversationId;

  const AIAssistantConversationRemoved({required this.conversationId});

  @override
  List<Object?> get props => [conversationId];
}

class AIAssistantConversationListLoaded extends AIAssistantState {
  final List<ChatConversation> conversations;

  const AIAssistantConversationListLoaded({required this.conversations});

  @override
  List<Object?> get props => [conversations];
}
