import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import '../../models/ai_chat_model.dart';
import '../../models/resume_model.dart';
import '../../models/work_experience_model.dart';
import '../../models/education_model.dart';
import '../../models/skill_model.dart';
import '../../services/ai_assistant_service.dart';
import '../../services/firestore_service.dart';

part 'ai_assistant_event.dart';
part 'ai_assistant_state.dart';

class AIAssistantBloc extends Bloc<AIAssistantEvent, AIAssistantState> {
  final AIAssistantService _aiService;
  final FirestoreService _firestoreService;
  final Uuid _uuid = const Uuid();

  AIAssistantBloc({
    required AIAssistantService aiService,
    required FirestoreService firestoreService,
  })  : _aiService = aiService,
        _firestoreService = firestoreService,
        super(AIAssistantInitial()) {
    on<AIAssistantStarted>(_onStarted);
    on<AIAssistantMessageSent>(_onMessageSent);
    on<AIAssistantConversationLoaded>(_onConversationLoaded);
    on<AIAssistantConversationCreated>(_onConversationCreated);
    on<AIAssistantConversationDeleted>(_onConversationDeleted);
    on<AIAssistantConversationsLoaded>(_onConversationsLoaded);
    on<AIAssistantContextChanged>(_onContextChanged);
    on<AIAssistantJobDescriptionRequested>(_onJobDescriptionRequested);
    on<AIAssistantSkillSuggestionsRequested>(_onSkillSuggestionsRequested);
    on<AIAssistantSummaryRequested>(_onSummaryRequested);
    on<AIAssistantATSOptimizationRequested>(_onATSOptimizationRequested);
    on<AIAssistantImprovementSuggestionsRequested>(_onImprovementSuggestionsRequested);
    on<AIAssistantMessageRetried>(_onMessageRetried);
    on<AIAssistantCleared>(_onCleared);
  }

  Future<void> _onStarted(AIAssistantStarted event, Emitter<AIAssistantState> emit) async {
    emit(const AIAssistantLoaded());
  }

  Future<void> _onMessageSent(AIAssistantMessageSent event, Emitter<AIAssistantState> emit) async {
    try {
      // Check if AI service is initialized
      if (!_aiService.isInitialized) {
        emit(AIAssistantError(
          message: 'AI Assistant is not properly initialized. Please check your configuration.',
          conversation: state is AIAssistantLoaded ? (state as AIAssistantLoaded).currentConversation : null,
        ));
        return;
      }

      // Get current state
      final currentState = state;
      ChatConversation? conversation;

      if (currentState is AIAssistantLoaded) {
        conversation = currentState.currentConversation;
      }

      // Create new conversation if none exists
      conversation ??= ChatConversation(
        id: _uuid.v4(),
        title: _generateConversationTitle(event.message),
        messages: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create user message
      final userMessage = ChatMessage(
        id: _uuid.v4(),
        content: event.message,
        type: MessageType.user,
        timestamp: DateTime.now(),
        status: MessageStatus.sent,
      );

      // Add user message to conversation
      conversation = conversation.addMessage(userMessage);

      // Create pending assistant message
      final pendingMessage = ChatMessage(
        id: _uuid.v4(),
        content: '',
        type: MessageType.assistant,
        timestamp: DateTime.now(),
        status: MessageStatus.sending,
      );

      // Add pending message to conversation
      conversation = conversation.addMessage(pendingMessage);

      emit(AIAssistantMessageSending(
        conversation: conversation,
        pendingMessage: pendingMessage,
      ));

      // Get AI response with timeout
      final response = await _aiService.sendMessage(
        message: event.message,
        context: event.context,
        conversationHistory: conversation.messages.where((msg) => msg.status == MessageStatus.sent).toList(),
        additionalContext: event.additionalContext,
        timeout: const Duration(seconds: 30),
      );

      // Create assistant message
      final assistantMessage = pendingMessage.copyWith(
        content: response,
        status: MessageStatus.sent,
      );

      // Update conversation with the completed assistant message
      conversation = conversation.updateMessage(pendingMessage.id, assistantMessage);

      if (kDebugMode) {
        print('🔄 Final conversation has ${conversation.messages.length} messages');
        for (int i = 0; i < conversation.messages.length; i++) {
          final msg = conversation.messages[i];
          print('   Message $i: ${msg.type.name} - "${msg.content.length > 50 ? msg.content.substring(0, 50) + '...' : msg.content}"');
        }
      }

      // Emit loaded state immediately with the complete conversation
      emit(AIAssistantLoaded(
        currentConversation: conversation,
        currentContext: event.context,
      ));

      // Save conversation to Firestore (optional)
      // await _firestoreService.saveConversation(conversation);

    } on TimeoutException catch (e) {
      // Handle timeout specifically
      final currentState = state;
      ChatConversation? conversation;

      if (currentState is AIAssistantMessageSending) {
        conversation = currentState.conversation;
        // Update the pending message to show error
        final errorMessage = currentState.pendingMessage.copyWith(
          content: 'Request timed out. Please try again.',
          status: MessageStatus.error,
          error: e.toString(),
        );
        conversation = conversation.updateMessage(currentState.pendingMessage.id, errorMessage);
      }

      emit(AIAssistantError(
        message: 'Request timed out. Please check your internet connection and try again.',
        conversation: conversation,
      ));
    } catch (e) {
      // Handle other errors
      final currentState = state;
      ChatConversation? conversation;

      if (currentState is AIAssistantMessageSending) {
        conversation = currentState.conversation;
        // Update the pending message to show error
        final errorMessage = currentState.pendingMessage.copyWith(
          content: 'Failed to get response. Please try again.',
          status: MessageStatus.error,
          error: e.toString(),
        );
        conversation = conversation.updateMessage(currentState.pendingMessage.id, errorMessage);
      }

      // Provide user-friendly error messages
      String userFriendlyMessage = 'Failed to send message. Please try again.';

      if (e.toString().contains('API key')) {
        userFriendlyMessage = 'AI service configuration error. Please contact support.';
      } else if (e.toString().contains('quota') || e.toString().contains('limit')) {
        userFriendlyMessage = 'AI service is temporarily unavailable. Please try again later.';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        userFriendlyMessage = 'Network error. Please check your internet connection.';
      }

      emit(AIAssistantError(
        message: userFriendlyMessage,
        conversation: conversation,
      ));
    }
  }

  Future<void> _onConversationLoaded(AIAssistantConversationLoaded event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      // Load conversation from Firestore
      // final conversation = await _firestoreService.getConversation(event.conversationId);
      
      // For now, emit loaded state with empty conversation
      emit(const AIAssistantLoaded());
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to load conversation: $e'));
    }
  }

  Future<void> _onConversationCreated(AIAssistantConversationCreated event, Emitter<AIAssistantState> emit) async {
    try {
      final conversation = ChatConversation(
        id: _uuid.v4(),
        title: event.title,
        messages: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        context: {
          'assistantContext': event.context.name,
          ...?event.initialContext,
        },
      );

      emit(AIAssistantNewConversationCreated(conversation: conversation));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to create conversation: $e'));
    }
  }

  Future<void> _onConversationDeleted(AIAssistantConversationDeleted event, Emitter<AIAssistantState> emit) async {
    try {
      // Delete from Firestore
      // await _firestoreService.deleteConversation(event.conversationId);
      
      emit(AIAssistantConversationRemoved(conversationId: event.conversationId));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to delete conversation: $e'));
    }
  }

  Future<void> _onConversationsLoaded(AIAssistantConversationsLoaded event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      // Load conversations from Firestore
      // final conversations = await _firestoreService.getUserConversations(event.userId);
      
      // For now, emit empty list
      emit(const AIAssistantConversationListLoaded(conversations: []));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to load conversations: $e'));
    }
  }

  Future<void> _onContextChanged(AIAssistantContextChanged event, Emitter<AIAssistantState> emit) async {
    final currentState = state;
    if (currentState is AIAssistantLoaded) {
      emit(currentState.copyWith(
        currentContext: event.context,
        additionalContext: event.additionalContext,
      ));
    }
  }

  Future<void> _onJobDescriptionRequested(AIAssistantJobDescriptionRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final bullets = await _aiService.generateJobDescriptionBullets(
        jobTitle: event.jobTitle,
        company: event.company,
        industry: event.industry,
        currentResponsibilities: event.currentResponsibilities,
        additionalContext: event.additionalContext,
      );

      emit(AIAssistantJobDescriptionGenerated(
        bullets: bullets,
        jobTitle: event.jobTitle,
        company: event.company,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate job description: $e'));
    }
  }

  Future<void> _onSkillSuggestionsRequested(AIAssistantSkillSuggestionsRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final skills = await _aiService.generateSkillSuggestions(
        industry: event.industry,
        jobTitle: event.jobTitle,
        currentSkills: event.currentSkills,
        experienceYears: event.experienceYears,
      );

      emit(AIAssistantSkillSuggestionsGenerated(
        skills: skills,
        jobTitle: event.jobTitle,
        industry: event.industry,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate skill suggestions: $e'));
    }
  }

  Future<void> _onSummaryRequested(AIAssistantSummaryRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final summary = await _aiService.generateProfessionalSummary(
        personalInfo: event.personalInfo,
        workExperience: event.workExperience,
        education: event.education,
        skills: event.skills,
        targetIndustry: event.targetIndustry,
        targetJobTitle: event.targetJobTitle,
      );

      emit(AIAssistantSummaryGenerated(
        summary: summary,
        targetJobTitle: event.targetJobTitle,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate summary: $e'));
    }
  }

  Future<void> _onATSOptimizationRequested(AIAssistantATSOptimizationRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final optimizedContent = await _aiService.optimizeForATS(
        content: event.content,
        jobDescription: event.jobDescription,
        targetKeywords: event.targetKeywords,
      );

      emit(AIAssistantATSOptimizationGenerated(
        optimizedContent: optimizedContent,
        originalContent: event.content,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to optimize for ATS: $e'));
    }
  }

  Future<void> _onImprovementSuggestionsRequested(AIAssistantImprovementSuggestionsRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final suggestions = await _aiService.getResumeImprovementSuggestions(
        resume: event.resume,
        targetJobDescription: event.targetJobDescription,
      );

      emit(AIAssistantImprovementSuggestionsGenerated(
        suggestions: suggestions,
        resume: event.resume,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate improvement suggestions: $e'));
    }
  }

  Future<void> _onMessageRetried(AIAssistantMessageRetried event, Emitter<AIAssistantState> emit) async {
    // Implement message retry logic
    // This would resend a failed message
  }

  Future<void> _onCleared(AIAssistantCleared event, Emitter<AIAssistantState> emit) async {
    emit(AIAssistantInitial());
  }

  String _generateConversationTitle(String firstMessage) {
    // Generate a title based on the first message
    final words = firstMessage.split(' ').take(5).join(' ');
    return words.length > 30 ? '${words.substring(0, 30)}...' : words;
  }
}
