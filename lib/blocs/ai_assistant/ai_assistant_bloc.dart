import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';
import '../../models/ai_chat_model.dart';
import '../../models/resume_model.dart';
import '../../models/work_experience_model.dart';
import '../../models/education_model.dart';
import '../../models/skill_model.dart';
import '../../services/ai_assistant_service.dart';
import '../../services/firestore_service.dart';

part 'ai_assistant_event.dart';
part 'ai_assistant_state.dart';

class AIAssistantBloc extends Bloc<AIAssistantEvent, AIAssistantState> {
  final AIAssistantService _aiService;
  final FirestoreService _firestoreService;
  final Uuid _uuid = const Uuid();

  AIAssistantBloc({
    required AIAssistantService aiService,
    required FirestoreService firestoreService,
  })  : _aiService = aiService,
        _firestoreService = firestoreService,
        super(AIAssistantInitial()) {
    on<AIAssistantStarted>(_onStarted);
    on<AIAssistantMessageSent>(_onMessageSent);
    on<AIAssistantConversationLoaded>(_onConversationLoaded);
    on<AIAssistantConversationCreated>(_onConversationCreated);
    on<AIAssistantConversationDeleted>(_onConversationDeleted);
    on<AIAssistantConversationsLoaded>(_onConversationsLoaded);
    on<AIAssistantContextChanged>(_onContextChanged);
    on<AIAssistantJobDescriptionRequested>(_onJobDescriptionRequested);
    on<AIAssistantSkillSuggestionsRequested>(_onSkillSuggestionsRequested);
    on<AIAssistantSummaryRequested>(_onSummaryRequested);
    on<AIAssistantATSOptimizationRequested>(_onATSOptimizationRequested);
    on<AIAssistantImprovementSuggestionsRequested>(_onImprovementSuggestionsRequested);
    on<AIAssistantMessageRetried>(_onMessageRetried);
    on<AIAssistantCleared>(_onCleared);
  }

  Future<void> _onStarted(AIAssistantStarted event, Emitter<AIAssistantState> emit) async {
    emit(const AIAssistantLoaded());
  }

  Future<void> _onMessageSent(AIAssistantMessageSent event, Emitter<AIAssistantState> emit) async {
    try {
      // Get current state
      final currentState = state;
      ChatConversation? conversation;
      
      if (currentState is AIAssistantLoaded) {
        conversation = currentState.currentConversation;
      }

      // Create new conversation if none exists
      conversation ??= ChatConversation(
        id: _uuid.v4(),
        title: _generateConversationTitle(event.message),
        messages: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create user message
      final userMessage = ChatMessage(
        id: _uuid.v4(),
        content: event.message,
        type: MessageType.user,
        timestamp: DateTime.now(),
        status: MessageStatus.sent,
      );

      // Add user message to conversation
      conversation = conversation.addMessage(userMessage);

      // Create pending assistant message
      final pendingMessage = ChatMessage(
        id: _uuid.v4(),
        content: '',
        type: MessageType.assistant,
        timestamp: DateTime.now(),
        status: MessageStatus.sending,
      );

      emit(AIAssistantMessageSending(
        conversation: conversation.addMessage(pendingMessage),
        pendingMessage: pendingMessage,
      ));

      // Get AI response
      final response = await _aiService.sendMessage(
        message: event.message,
        context: event.context,
        conversationHistory: conversation.messages,
        additionalContext: event.additionalContext,
      );

      // Create assistant message
      final assistantMessage = pendingMessage.copyWith(
        content: response,
        status: MessageStatus.sent,
      );

      // Update conversation
      conversation = conversation.updateMessage(pendingMessage.id, assistantMessage);

      emit(AIAssistantMessageReceived(
        conversation: conversation,
        userMessage: userMessage,
        assistantMessage: assistantMessage,
      ));

      // Save conversation to Firestore (optional)
      // await _firestoreService.saveConversation(conversation);

    } catch (e) {
      emit(AIAssistantError(
        message: 'Failed to send message: $e',
        conversation: state is AIAssistantLoaded ? (state as AIAssistantLoaded).currentConversation : null,
      ));
    }
  }

  Future<void> _onConversationLoaded(AIAssistantConversationLoaded event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      // Load conversation from Firestore
      // final conversation = await _firestoreService.getConversation(event.conversationId);
      
      // For now, emit loaded state with empty conversation
      emit(const AIAssistantLoaded());
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to load conversation: $e'));
    }
  }

  Future<void> _onConversationCreated(AIAssistantConversationCreated event, Emitter<AIAssistantState> emit) async {
    try {
      final conversation = ChatConversation(
        id: _uuid.v4(),
        title: event.title,
        messages: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        context: {
          'assistantContext': event.context.name,
          ...?event.initialContext,
        },
      );

      emit(AIAssistantNewConversationCreated(conversation: conversation));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to create conversation: $e'));
    }
  }

  Future<void> _onConversationDeleted(AIAssistantConversationDeleted event, Emitter<AIAssistantState> emit) async {
    try {
      // Delete from Firestore
      // await _firestoreService.deleteConversation(event.conversationId);
      
      emit(AIAssistantConversationRemoved(conversationId: event.conversationId));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to delete conversation: $e'));
    }
  }

  Future<void> _onConversationsLoaded(AIAssistantConversationsLoaded event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      // Load conversations from Firestore
      // final conversations = await _firestoreService.getUserConversations(event.userId);
      
      // For now, emit empty list
      emit(const AIAssistantConversationListLoaded(conversations: []));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to load conversations: $e'));
    }
  }

  Future<void> _onContextChanged(AIAssistantContextChanged event, Emitter<AIAssistantState> emit) async {
    final currentState = state;
    if (currentState is AIAssistantLoaded) {
      emit(currentState.copyWith(
        currentContext: event.context,
        additionalContext: event.additionalContext,
      ));
    }
  }

  Future<void> _onJobDescriptionRequested(AIAssistantJobDescriptionRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final bullets = await _aiService.generateJobDescriptionBullets(
        jobTitle: event.jobTitle,
        company: event.company,
        industry: event.industry,
        currentResponsibilities: event.currentResponsibilities,
        additionalContext: event.additionalContext,
      );

      emit(AIAssistantJobDescriptionGenerated(
        bullets: bullets,
        jobTitle: event.jobTitle,
        company: event.company,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate job description: $e'));
    }
  }

  Future<void> _onSkillSuggestionsRequested(AIAssistantSkillSuggestionsRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final skills = await _aiService.generateSkillSuggestions(
        industry: event.industry,
        jobTitle: event.jobTitle,
        currentSkills: event.currentSkills,
        experienceYears: event.experienceYears,
      );

      emit(AIAssistantSkillSuggestionsGenerated(
        skills: skills,
        jobTitle: event.jobTitle,
        industry: event.industry,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate skill suggestions: $e'));
    }
  }

  Future<void> _onSummaryRequested(AIAssistantSummaryRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final summary = await _aiService.generateProfessionalSummary(
        personalInfo: event.personalInfo,
        workExperience: event.workExperience,
        education: event.education,
        skills: event.skills,
        targetIndustry: event.targetIndustry,
        targetJobTitle: event.targetJobTitle,
      );

      emit(AIAssistantSummaryGenerated(
        summary: summary,
        targetJobTitle: event.targetJobTitle,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate summary: $e'));
    }
  }

  Future<void> _onATSOptimizationRequested(AIAssistantATSOptimizationRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final optimizedContent = await _aiService.optimizeForATS(
        content: event.content,
        jobDescription: event.jobDescription,
        targetKeywords: event.targetKeywords,
      );

      emit(AIAssistantATSOptimizationGenerated(
        optimizedContent: optimizedContent,
        originalContent: event.content,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to optimize for ATS: $e'));
    }
  }

  Future<void> _onImprovementSuggestionsRequested(AIAssistantImprovementSuggestionsRequested event, Emitter<AIAssistantState> emit) async {
    try {
      emit(AIAssistantLoading());
      
      final suggestions = await _aiService.getResumeImprovementSuggestions(
        resume: event.resume,
        targetJobDescription: event.targetJobDescription,
      );

      emit(AIAssistantImprovementSuggestionsGenerated(
        suggestions: suggestions,
        resume: event.resume,
      ));
    } catch (e) {
      emit(AIAssistantError(message: 'Failed to generate improvement suggestions: $e'));
    }
  }

  Future<void> _onMessageRetried(AIAssistantMessageRetried event, Emitter<AIAssistantState> emit) async {
    // Implement message retry logic
    // This would resend a failed message
  }

  Future<void> _onCleared(AIAssistantCleared event, Emitter<AIAssistantState> emit) async {
    emit(AIAssistantInitial());
  }

  String _generateConversationTitle(String firstMessage) {
    // Generate a title based on the first message
    final words = firstMessage.split(' ').take(5).join(' ');
    return words.length > 30 ? '${words.substring(0, 30)}...' : words;
  }
}
