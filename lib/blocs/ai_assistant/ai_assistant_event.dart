part of 'ai_assistant_bloc.dart';

abstract class AIAssistantEvent extends Equatable {
  const AIAssistantEvent();

  @override
  List<Object?> get props => [];
}

class AIAssistantStarted extends AIAssistantEvent {}

class AIAssistantMessageSent extends AIAssistantEvent {
  final String message;
  final AIAssistantContext context;
  final Map<String, dynamic>? additionalContext;

  const AIAssistantMessageSent({
    required this.message,
    required this.context,
    this.additionalContext,
  });

  @override
  List<Object?> get props => [message, context, additionalContext];
}

class AIAssistantConversationLoaded extends AIAssistantEvent {
  final String conversationId;

  const AIAssistantConversationLoaded({required this.conversationId});

  @override
  List<Object?> get props => [conversationId];
}

class AIAssistantConversationCreated extends AIAssistantEvent {
  final String title;
  final AIAssistantContext context;
  final Map<String, dynamic>? initialContext;

  const AIAssistantConversationCreated({
    required this.title,
    required this.context,
    this.initialContext,
  });

  @override
  List<Object?> get props => [title, context, initialContext];
}

class AIAssistantConversationDeleted extends AIAssistantEvent {
  final String conversationId;

  const AIAssistantConversationDeleted({required this.conversationId});

  @override
  List<Object?> get props => [conversationId];
}

class AIAssistantConversationsLoaded extends AIAssistantEvent {
  final String userId;

  const AIAssistantConversationsLoaded({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class AIAssistantContextChanged extends AIAssistantEvent {
  final AIAssistantContext context;
  final Map<String, dynamic>? additionalContext;

  const AIAssistantContextChanged({
    required this.context,
    this.additionalContext,
  });

  @override
  List<Object?> get props => [context, additionalContext];
}

class AIAssistantJobDescriptionRequested extends AIAssistantEvent {
  final String jobTitle;
  final String company;
  final String industry;
  final List<String>? currentResponsibilities;
  final String? additionalContext;

  const AIAssistantJobDescriptionRequested({
    required this.jobTitle,
    required this.company,
    required this.industry,
    this.currentResponsibilities,
    this.additionalContext,
  });

  @override
  List<Object?> get props => [
        jobTitle,
        company,
        industry,
        currentResponsibilities,
        additionalContext,
      ];
}

class AIAssistantSkillSuggestionsRequested extends AIAssistantEvent {
  final String industry;
  final String jobTitle;
  final List<Skill>? currentSkills;
  final int? experienceYears;

  const AIAssistantSkillSuggestionsRequested({
    required this.industry,
    required this.jobTitle,
    this.currentSkills,
    this.experienceYears,
  });

  @override
  List<Object?> get props => [industry, jobTitle, currentSkills, experienceYears];
}

class AIAssistantSummaryRequested extends AIAssistantEvent {
  final PersonalInfo personalInfo;
  final List<WorkExperience> workExperience;
  final List<Education> education;
  final List<Skill> skills;
  final String? targetIndustry;
  final String? targetJobTitle;

  const AIAssistantSummaryRequested({
    required this.personalInfo,
    required this.workExperience,
    required this.education,
    required this.skills,
    this.targetIndustry,
    this.targetJobTitle,
  });

  @override
  List<Object?> get props => [
        personalInfo,
        workExperience,
        education,
        skills,
        targetIndustry,
        targetJobTitle,
      ];
}

class AIAssistantATSOptimizationRequested extends AIAssistantEvent {
  final String content;
  final String jobDescription;
  final String? targetKeywords;

  const AIAssistantATSOptimizationRequested({
    required this.content,
    required this.jobDescription,
    this.targetKeywords,
  });

  @override
  List<Object?> get props => [content, jobDescription, targetKeywords];
}

class AIAssistantImprovementSuggestionsRequested extends AIAssistantEvent {
  final ResumeModel resume;
  final String? targetJobDescription;

  const AIAssistantImprovementSuggestionsRequested({
    required this.resume,
    this.targetJobDescription,
  });

  @override
  List<Object?> get props => [resume, targetJobDescription];
}

class AIAssistantMessageRetried extends AIAssistantEvent {
  final String messageId;

  const AIAssistantMessageRetried({required this.messageId});

  @override
  List<Object?> get props => [messageId];
}

class AIAssistantCleared extends AIAssistantEvent {}
